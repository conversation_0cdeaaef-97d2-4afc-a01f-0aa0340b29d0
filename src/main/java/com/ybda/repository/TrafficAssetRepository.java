package com.ybda.repository;

import com.ybda.entity.TrafficAsset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 交通资产数据访问接口
 */
@Repository
public interface TrafficAssetRepository extends JpaRepository<TrafficAsset, Long> {

    /**
     * 根据资产ID查找
     */
    Optional<TrafficAsset> findByAssetId(String assetId);

    /**
     * 根据类型查找资产
     */
    List<TrafficAsset> findByType(String type);

    /**
     * 根据状态查找资产
     */
    List<TrafficAsset> findByStatus(String status);

    /**
     * 在指定地理范围内查找资产
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @return 范围内的资产列表
     */
    @Query("SELECT ta FROM TrafficAsset ta WHERE ta.latitude BETWEEN :minLat AND :maxLat " +
           "AND ta.longitude BETWEEN :minLng AND :maxLng")
    List<TrafficAsset> findAssetsInBounds(@Param("minLat") Double minLat,
                                         @Param("maxLat") Double maxLat,
                                         @Param("minLng") Double minLng,
                                         @Param("maxLng") Double maxLng);

    /**
     * 查找指定位置附近的资产（用于数据融合）
     * @param latitude 纬度
     * @param longitude 经度
     * @param distance 距离范围（米）
     * @return 附近的资产列表
     */
    @Query(value = "SELECT * FROM traffic_assets ta WHERE " +
           "(6371000 * acos(cos(radians(:latitude)) * cos(radians(ta.latitude)) * " +
           "cos(radians(ta.longitude) - radians(:longitude)) + " +
           "sin(radians(:latitude)) * sin(radians(ta.latitude)))) <= :distance",
           nativeQuery = true)
    List<TrafficAsset> findNearbyAssets(@Param("latitude") Double latitude,
                                       @Param("longitude") Double longitude,
                                       @Param("distance") Double distance);

    /**
     * 根据类型和名称查找资产
     */
    List<TrafficAsset> findByTypeAndName(String type, String name);
}
