package com.ybda.repository;

import com.ybda.entity.DetectionDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 检测详情数据访问接口
 */
@Repository
public interface DetectionDetailRepository extends JpaRepository<DetectionDetail, Long> {

    /**
     * 根据检测记录ID查找详情
     */
    List<DetectionDetail> findByDetectionRecordId(Long detectionRecordId);

    /**
     * 根据类型查找检测详情
     */
    List<DetectionDetail> findByType(String type);

    /**
     * 根据跟踪ID查找检测详情
     */
    List<DetectionDetail> findByTrackId(Integer trackId);

    /**
     * 根据交通资产ID查找相关检测详情
     */
    List<DetectionDetail> findByTrafficAssetId(String trafficAssetId);

    /**
     * 查找未关联到交通资产的检测详情
     */
    @Query("SELECT dd FROM DetectionDetail dd WHERE dd.trafficAssetId IS NULL")
    List<DetectionDetail> findUnlinkedDetails();

    /**
     * 统计各类型的检测数量
     */
    @Query("SELECT dd.type, COUNT(dd) FROM DetectionDetail dd GROUP BY dd.type")
    List<Object[]> countByType();
}
