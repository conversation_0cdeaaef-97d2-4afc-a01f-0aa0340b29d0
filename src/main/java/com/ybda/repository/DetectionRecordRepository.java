package com.ybda.repository;

import com.ybda.entity.DetectionRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 检测记录数据访问接口
 */
@Repository
public interface DetectionRecordRepository extends JpaRepository<DetectionRecord, Long> {

    /**
     * 根据帧ID查找检测记录
     */
    Optional<DetectionRecord> findByFrameId(String frameId);

    /**
     * 根据处理状态查找记录
     */
    List<DetectionRecord> findByProcessStatus(String processStatus);

    /**
     * 根据设备ID查找记录
     */
    List<DetectionRecord> findByDeviceId(String deviceId);

    /**
     * 查找指定时间范围内的记录
     */
    @Query("SELECT dr FROM DetectionRecord dr WHERE dr.createdTime BETWEEN :startTime AND :endTime")
    List<DetectionRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查找待处理的记录
     */
    @Query("SELECT dr FROM DetectionRecord dr WHERE dr.processStatus = 'PENDING' ORDER BY dr.createdTime ASC")
    List<DetectionRecord> findPendingRecords();

    /**
     * 统计指定状态的记录数量
     */
    long countByProcessStatus(String processStatus);
}
