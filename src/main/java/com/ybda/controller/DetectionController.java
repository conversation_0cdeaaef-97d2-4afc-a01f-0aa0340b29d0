package com.ybda.controller;

import com.ybda.dto.DetectionRequestDTO;
import com.ybda.service.DetectionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 检测数据接收控制器
 * 提供接收视频分析系统检测数据的API接口
 */
@RestController
@RequestMapping("/api/detection")
@RequiredArgsConstructor
@Slf4j
public class DetectionController {

    private final DetectionService detectionService;

    /**
     * 接收检测数据
     * @param request 检测请求数据
     * @return 处理结果
     */
    @PostMapping("/receive")
    public ResponseEntity<Map<String, Object>> receiveDetectionData(
            @Valid @RequestBody DetectionRequestDTO request) {
        
        log.info("接收检测数据请求，帧ID: {}", request.getFrameId());
        
        Map<String, Object> response = new HashMap<>();2
        
        try {
            String result = detectionService.processDetectionRequest(request);
            
            response.put("success", true);
            response.put("message", result);
            response.put("frameId", request.getFrameId());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("处理检测数据失败", e);
            
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            response.put("frameId", request.getFrameId());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取处理状态统计
     * @return 处理状态统计信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getProcessingStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            long pendingCount = detectionService.getPendingRecordsCount();
            long failedCount = detectionService.getFailedRecordsCount();
            
            status.put("success", true);
            status.put("pendingRecords", pendingCount);
            status.put("failedRecords", failedCount);
            status.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("获取处理状态失败", e);
            
            status.put("success", false);
            status.put("message", "获取状态失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(status);
        }
    }

    /**
     * 健康检查接口
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "detection-service");
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
}
