package com.ybda.controller;

import com.ybda.entity.GPSTrack;
import com.ybda.service.AssetMissingDetectionService;
import com.ybda.service.GPSTrackingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GPS轨迹管理控制器
 * 提供GPS轨迹查询和资产缺失检测的手动触发功能
 */
@RestController
@RequestMapping("/api/gps")
@RequiredArgsConstructor
@Slf4j
public class GPSTrackController {

    private final GPSTrackingService gpsTrackingService;
    private final AssetMissingDetectionService assetMissingDetectionService;

    /**
     * 获取GPS轨迹统计信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getGPSStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long unprocessedCount = gpsTrackingService.getUnprocessedTrackCount();
            
            response.put("success", true);
            response.put("unprocessedTracks", unprocessedCount);
            response.put("trackingActive", true);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取GPS状态失败", e);
            
            response.put("success", false);
            response.put("message", "获取GPS状态失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取指定时间范围内的GPS轨迹
     */
    @GetMapping("/tracks")
    public ResponseEntity<Map<String, Object>> getTracks(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<GPSTrack> tracks = gpsTrackingService.getTracksByTimeRange(startTime, endTime);
            
            response.put("success", true);
            response.put("data", tracks);
            response.put("count", tracks.size());
            response.put("startTime", startTime);
            response.put("endTime", endTime);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取GPS轨迹失败", e);
            
            response.put("success", false);
            response.put("message", "获取GPS轨迹失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 手动触发GPS轨迹处理
     */
    @PostMapping("/process")
    public ResponseEntity<Map<String, Object>> triggerTrackProcessing() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            gpsTrackingService.triggerTrackProcessing();
            
            response.put("success", true);
            response.put("message", "GPS轨迹处理已触发");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("触发GPS轨迹处理失败", e);
            
            response.put("success", false);
            response.put("message", "触发处理失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 手动触发指定区域的资产缺失检测
     */
    @PostMapping("/detect-missing")
    public ResponseEntity<Map<String, Object>> triggerMissingDetection(
            @RequestParam Double latitude,
            @RequestParam Double longitude,
            @RequestParam(defaultValue = "50.0") Double radius) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            assetMissingDetectionService.triggerMissingDetectionForArea(latitude, longitude, radius);
            
            response.put("success", true);
            response.put("message", "资产缺失检测已触发");
            response.put("area", Map.of(
                "latitude", latitude,
                "longitude", longitude,
                "radius", radius
            ));
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("触发资产缺失检测失败", e);
            
            response.put("success", false);
            response.put("message", "触发检测失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            long unprocessedCount = gpsTrackingService.getUnprocessedTrackCount();
            
            health.put("status", "UP");
            health.put("service", "gps-tracking-service");
            health.put("unprocessedTracks", unprocessedCount);
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(health);
        }
    }
}
