package com.ybda.controller;

import com.ybda.entity.TrafficAsset;
import com.ybda.mapper.TrafficAssetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 交通资产管理控制器
 * 提供交通资产的基础管理功能
 */
@RestController
@RequestMapping("/api/assets")
@RequiredArgsConstructor
@Slf4j
public class AssetController {

    private final TrafficAssetMapper trafficAssetMapper;

    /**
     * 获取资产列表（分页）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param type 资产类型（可选）
     * @param status 资产状态（可选）
     * @return 资产列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAssets(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {

        Map<String, Object> response = new HashMap<>();

        try {
            List<TrafficAsset> assets;
            long totalCount;

            if (type != null) {
                assets = trafficAssetMapper.findByType(type);
                totalCount = assets.size();
            } else if (status != null) {
                assets = trafficAssetMapper.findByStatus(status);
                totalCount = assets.size();
            } else {
                int offset = page * size;
                assets = trafficAssetMapper.findAllWithPaging(offset, size);
                totalCount = trafficAssetMapper.countAll();
            }

            response.put("success", true);
            response.put("data", assets);
            response.put("totalElements", totalCount);
            response.put("totalPages", (totalCount + size - 1) / size);
            response.put("currentPage", page);
            response.put("pageSize", size);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取资产列表失败", e);

            response.put("success", false);
            response.put("message", "获取资产列表失败: " + e.getMessage());

            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取资产详情
     * @param id 资产ID
     * @return 资产详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getAssetById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            TrafficAsset asset = trafficAssetMapper.findById(id);

            if (asset != null) {
                response.put("success", true);
                response.put("data", asset);
            } else {
                response.put("success", false);
                response.put("message", "资产不存在");
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取资产详情失败，ID: {}", id, e);

            response.put("success", false);
            response.put("message", "获取资产详情失败: " + e.getMessage());

            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 在指定地理范围内查找资产
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @return 范围内的资产列表
     */
    @GetMapping("/bounds")
    public ResponseEntity<Map<String, Object>> getAssetsInBounds(
            @RequestParam Double minLat,
            @RequestParam Double maxLat,
            @RequestParam Double minLng,
            @RequestParam Double maxLng) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<TrafficAsset> assets = trafficAssetMapper.findAssetsInBounds(
                    minLat, maxLat, minLng, maxLng);

            response.put("success", true);
            response.put("data", assets);
            response.put("count", assets.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查找范围内资产失败", e);

            response.put("success", false);
            response.put("message", "查找范围内资产失败: " + e.getMessage());

            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取资产统计信息
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getAssetStatistics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long totalAssets = trafficAssetMapper.countAll();
            long activeAssets = trafficAssetMapper.countByStatus("ACTIVE");
            long missingAssets = trafficAssetMapper.countByStatus("MISSING");
            long inactiveAssets = trafficAssetMapper.countByStatus("INACTIVE");

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalAssets", totalAssets);
            stats.put("activeAssets", activeAssets);
            stats.put("missingAssets", missingAssets);
            stats.put("inactiveAssets", inactiveAssets);

            response.put("success", true);
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取资产统计信息失败", e);

            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());

            return ResponseEntity.internalServerError().body(response);
        }
    }
}
