package com.ybda;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan("com.ybda.mapper")
@EnableScheduling
@EnableAsync
public class AssetInspectionVehicleApplication {

    public static void main(String[] args) {
        SpringApplication.run(AssetInspectionVehicleApplication.class, args);
    }

}
