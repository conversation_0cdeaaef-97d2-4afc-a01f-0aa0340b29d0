package com.ybda.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.entity.DetectionRecord;
import com.ybda.entity.GPSTrack;
import com.ybda.entity.PendingAssetCheck;
import com.ybda.entity.TrafficAsset;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.GPSTrackMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资产缺失检测服务
 * 新逻辑：暂存+延迟检测
 * 1. 每秒检查GPS是否经过资产位置，如果是则创建待确认记录
 * 2. 2分钟后检查是否收到对应的POST请求，没有则报警
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetMissingDetectionService {

    private final TrafficAssetMapper trafficAssetMapper;
    private final DetectionRecordMapper detectionRecordMapper;
    private final GPSTrackMapper gpsTrackMapper;
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final AssetAlertService assetAlertService;
    private final ObjectMapper objectMapper;

    // 资产位置匹配的距离阈值（米）
    private static final double ASSET_PROXIMITY_DISTANCE = 15.0;
    // 等待POST请求的超时时间（分钟）
    private static final int PENDING_TIMEOUT_MINUTES = 2;

    /**
     * 检查GPS轨迹是否经过资产位置，使用状态机避免重复创建
     * @param gpsTracks GPS轨迹列表
     */
    @Transactional
    public void checkGPSTracksForAssets(List<GPSTrack> gpsTracks) {
        log.debug("使用状态机检查GPS轨迹，轨迹数量: {}", gpsTracks.size());

        List<PendingAssetCheck> pendingChecks = new ArrayList<>();

        for (GPSTrack currentTrack : gpsTracks) {
            try {
                // 获取当前GPS位置附近的资产
                List<TrafficAsset> currentNearbyAssets = trafficAssetMapper.findNearbyAssets(
                        currentTrack.getLatitude(), currentTrack.getLongitude(), ASSET_PROXIMITY_DISTANCE);

                Set<String> currentAssetIds = currentNearbyAssets.stream()
                        .map(TrafficAsset::getAssetId)
                        .collect(Collectors.toSet());

                // 获取上一个GPS轨迹的资产状态
                Set<String> previousAssetIds = getPreviousNearbyAssets(currentTrack.getDeviceId());

                // 状态机逻辑：检查资产集合的变化
                Set<String> newlyEnteredAssets = new HashSet<>(currentAssetIds);
                newlyEnteredAssets.removeAll(previousAssetIds); // 新进入的资产

                Set<String> exitedAssets = new HashSet<>(previousAssetIds);
                exitedAssets.removeAll(currentAssetIds); // 离开的资产

                // 为新进入的资产创建待确认记录
                for (String assetId : newlyEnteredAssets) {
                    TrafficAsset asset = currentNearbyAssets.stream()
                            .filter(a -> a.getAssetId().equals(assetId))
                            .findFirst()
                            .orElse(null);

                    if (asset != null) {
                        PendingAssetCheck pendingCheck = createPendingAssetCheck(currentTrack, asset);
                        pendingChecks.add(pendingCheck);
                        log.debug("新进入资产范围: {}", assetId);
                    }
                }

                // 记录离开的资产（可用于后续分析）
                if (!exitedAssets.isEmpty()) {
                    log.debug("离开资产范围: {}", exitedAssets);
                }

                // 更新当前轨迹的资产状态
                updateTrackNearbyAssets(currentTrack, currentAssetIds);

                // 标记GPS轨迹为已处理
                gpsTrackMapper.updateProcessedStatus(currentTrack.getId(), true);

            } catch (Exception e) {
                log.error("检查GPS轨迹失败，轨迹ID: {}", currentTrack.getId(), e);
            }
        }

        // 批量插入待确认记录
        if (!pendingChecks.isEmpty()) {
            pendingAssetCheckMapper.batchInsertPendingChecks(pendingChecks);
            log.info("状态机创建待确认记录 {} 条", pendingChecks.size());
        }
    }

    /**
     * 获取上一个GPS轨迹的附近资产集合
     */
    private Set<String> getPreviousNearbyAssets(String deviceId) {
        try {
            GPSTrack latestTrack = gpsTrackMapper.findLatestTrackByDeviceId(deviceId);
            if (latestTrack == null || latestTrack.getCurrentNearbyAssets() == null) {
                return new HashSet<>();
            }

            List<String> assetIds = objectMapper.readValue(
                    latestTrack.getCurrentNearbyAssets(),
                    new TypeReference<List<String>>() {});

            return new HashSet<>(assetIds);

        } catch (Exception e) {
            log.error("获取上一个GPS轨迹的资产状态失败", e);
            return new HashSet<>();
        }
    }

    /**
     * 更新GPS轨迹的当前附近资产状态
     */
    private void updateTrackNearbyAssets(GPSTrack track, Set<String> assetIds) {
        try {
            String assetIdsJson = objectMapper.writeValueAsString(new ArrayList<>(assetIds));
            track.setCurrentNearbyAssets(assetIdsJson);
        } catch (Exception e) {
            log.error("更新GPS轨迹资产状态失败", e);
            track.setCurrentNearbyAssets("[]");
        }
    }

    /**
     * 创建待确认资产检查记录
     */
    private PendingAssetCheck createPendingAssetCheck(GPSTrack track, TrafficAsset asset) {
        LocalDateTime now = LocalDateTime.now();

        return PendingAssetCheck.builder()
                .gpsTrackId(track.getId())
                .expectedAssetId(asset.getAssetId())
                .assetType(asset.getType())
                .assetName(asset.getName())
                .gpsLatitude(track.getLatitude())
                .gpsLongitude(track.getLongitude())
                .passedTime(track.getCreatedTime())
                .status("PENDING")
                .createdTime(now)
                .updatedTime(now)
                .build();
    }

    /**
     * 处理超时的待确认记录，触发缺失报警
     */
    @Transactional
    public void processTimeoutPendingChecks() {
        // 查找2分钟前创建的待确认记录
        LocalDateTime timeoutBefore = LocalDateTime.now().minusMinutes(PENDING_TIMEOUT_MINUTES);
        List<PendingAssetCheck> timeoutChecks = pendingAssetCheckMapper.findTimeoutPendingChecks(timeoutBefore);

        if (timeoutChecks.isEmpty()) {
            return;
        }

        log.info("发现超时的待确认记录 {} 条，开始检查是否收到POST请求", timeoutChecks.size());

        for (PendingAssetCheck pendingCheck : timeoutChecks) {
            try {
                // 检查是否收到了对应的POST请求
                boolean hasDetection = checkIfDetectionReceived(pendingCheck);

                if (hasDetection) {
                    // 收到了检测记录，标记为已确认
                    pendingCheck.setStatus("CONFIRMED");
                    pendingCheck.setConfirmedTime(LocalDateTime.now());
                    pendingCheck.setUpdatedTime(LocalDateTime.now());
                    pendingAssetCheckMapper.updateConfirmStatus(pendingCheck);

                    log.debug("资产检测已确认: {}", pendingCheck.getExpectedAssetId());
                } else {
                    // 没有收到检测记录，资产缺失！
                    handleMissingAsset(pendingCheck);
                }

            } catch (Exception e) {
                log.error("处理超时待确认记录失败，记录ID: {}", pendingCheck.getId(), e);
            }
        }
    }

    /**
     * 检查是否收到了对应的检测记录
     */
    private boolean checkIfDetectionReceived(PendingAssetCheck pendingCheck) {
        // 在经过时间前后5分钟内查找检测记录
        LocalDateTime startTime = pendingCheck.getPassedTime().minusMinutes(5);
        LocalDateTime endTime = pendingCheck.getPassedTime().plusMinutes(5);

        List<DetectionRecord> detections = detectionRecordMapper.findByTimeRange(startTime, endTime);

        // 检查是否有在相同位置检测到相同资产的记录
        for (DetectionRecord detection : detections) {
            if (detection.getGpsLatitude() == null || detection.getGpsLongitude() == null) {
                continue;
            }

            // 检查位置是否匹配
            double distance = calculateDistance(
                    detection.getGpsLatitude(), detection.getGpsLongitude(),
                    pendingCheck.getGpsLatitude(), pendingCheck.getGpsLongitude());

            if (distance <= ASSET_PROXIMITY_DISTANCE) {
                // 检查是否检测到了预期的资产
                boolean hasExpectedAsset = detection.getDetectionDetails().stream()
                        .anyMatch(detail -> detail.getType().equals(pendingCheck.getAssetType())
                                && detail.getName().equals(pendingCheck.getAssetName()));

                if (hasExpectedAsset) {
                    // 找到了对应的检测记录
                    pendingCheck.setConfirmedDetectionId(detection.getId());
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 处理缺失的资产（基于待确认记录）
     */
    private void handleMissingAsset(PendingAssetCheck pendingCheck) {
        log.warn("检测到资产缺失: 资产ID={}, 类型={}, 名称={}, GPS位置=({}, {}), 经过时间={}",
                pendingCheck.getExpectedAssetId(), pendingCheck.getAssetType(), pendingCheck.getAssetName(),
                pendingCheck.getGpsLatitude(), pendingCheck.getGpsLongitude(), pendingCheck.getPassedTime());

        // 获取资产信息
        TrafficAsset asset = trafficAssetMapper.findByAssetId(pendingCheck.getExpectedAssetId());
        if (asset == null) {
            log.error("找不到资产信息: {}", pendingCheck.getExpectedAssetId());
            return;
        }

        // 检查资产当前状态，避免重复报警
        if (!"MISSING".equals(asset.getStatus())) {
            // 更新资产状态为缺失
            asset.setStatus("MISSING");
            asset.setUpdatedTime(LocalDateTime.now());
            trafficAssetMapper.updateAsset(asset);

            // 发送缺失报警
            assetAlertService.sendMissingAssetAlert(asset);

            log.info("资产已标记为缺失并发送报警: {}", asset.getAssetId());
        } else {
            log.debug("资产已处于MISSING状态，跳过重复报警: {}", asset.getAssetId());
        }

        // 更新待确认记录状态
        pendingCheck.setStatus("MISSING");
        pendingCheck.setUpdatedTime(LocalDateTime.now());
        pendingAssetCheckMapper.updateConfirmStatus(pendingCheck);
    }

    /**
     * 当收到POST请求时，检查并确认待确认记录
     * 这个方法由DetectionService调用
     */
    @Transactional
    public void confirmPendingChecks(DetectionRecord detectionRecord) {
        if (detectionRecord.getGpsLatitude() == null || detectionRecord.getGpsLongitude() == null) {
            return;
        }

        // 查找该位置和时间附近的待确认记录
        LocalDateTime detectionTime = detectionRecord.getCreatedTime();
        LocalDateTime startTime = detectionTime.minusMinutes(5);
        LocalDateTime endTime = detectionTime.plusMinutes(5);

        List<PendingAssetCheck> nearbyPendingChecks = pendingAssetCheckMapper.findNearbyPendingChecks(
                detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude(),
                ASSET_PROXIMITY_DISTANCE, startTime, endTime);

        for (PendingAssetCheck pendingCheck : nearbyPendingChecks) {
            if (!"PENDING".equals(pendingCheck.getStatus())) {
                continue;
            }

            // 检查是否检测到了预期的资产
            boolean hasExpectedAsset = detectionRecord.getDetectionDetails().stream()
                    .anyMatch(detail -> detail.getType().equals(pendingCheck.getAssetType())
                            && detail.getName().equals(pendingCheck.getAssetName()));

            if (hasExpectedAsset) {
                // 确认该待确认记录
                pendingCheck.setStatus("CONFIRMED");
                pendingCheck.setConfirmedDetectionId(detectionRecord.getId());
                pendingCheck.setConfirmedTime(LocalDateTime.now());
                pendingCheck.setUpdatedTime(LocalDateTime.now());
                pendingAssetCheckMapper.updateConfirmStatus(pendingCheck);

                log.debug("待确认记录已确认: 资产ID={}, 检测记录ID={}",
                        pendingCheck.getExpectedAssetId(), detectionRecord.getId());
            }
        }
    }

    /**
     * 计算两点间距离（米）
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }

    /**
     * 手动触发指定区域的资产缺失检测
     */
    public void triggerMissingDetectionForArea(double latitude, double longitude, double radius) {
        log.info("手动触发区域资产缺失检测: 中心({}, {}), 半径{}米", latitude, longitude, radius);
        
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(1); // 检查最近1小时的轨迹
        
        List<GPSTrack> nearbyTracks = gpsTrackMapper.findNearbyTracks(
                latitude, longitude, radius, startTime, endTime);
        
        if (!nearbyTracks.isEmpty()) {
            processGPSTracks(nearbyTracks);
        } else {
            log.info("指定区域内没有找到GPS轨迹记录");
        }
    }
}
