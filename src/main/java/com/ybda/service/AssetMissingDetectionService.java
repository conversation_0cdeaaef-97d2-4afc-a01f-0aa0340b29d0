package com.ybda.service;

import com.ybda.entity.DetectionRecord;
import com.ybda.entity.GPSTrack;
import com.ybda.entity.TrafficAsset;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.GPSTrackMapper;
import com.ybda.mapper.TrafficAssetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资产缺失检测服务
 * 核心逻辑：通过GPS轨迹匹配已知资产位置，检测是否有资产缺失
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetMissingDetectionService {

    private final TrafficAssetMapper trafficAssetMapper;
    private final DetectionRecordMapper detectionRecordMapper;
    private final GPSTrackMapper gpsTrackMapper;
    private final AssetAlertService assetAlertService;

    // 资产位置匹配的距离阈值（米）
    private static final double ASSET_PROXIMITY_DISTANCE = 15.0;
    // 检测时间窗口（分钟）- 在轨迹时间前后X分钟内查找检测记录
    private static final int DETECTION_TIME_WINDOW_MINUTES = 2;

    /**
     * 处理GPS轨迹，进行资产缺失检测
     * @param gpsTracks GPS轨迹列表
     */
    @Transactional
    public void processGPSTracks(List<GPSTrack> gpsTracks) {
        log.info("开始处理GPS轨迹进行资产缺失检测，轨迹数量: {}", gpsTracks.size());

        for (GPSTrack track : gpsTracks) {
            try {
                processSingleTrack(track);
                
                // 标记为已处理
                gpsTrackMapper.updateProcessedStatus(track.getId(), true);
                
            } catch (Exception e) {
                log.error("处理GPS轨迹失败，轨迹ID: {}", track.getId(), e);
            }
        }

        log.info("GPS轨迹处理完成");
    }

    /**
     * 处理单个GPS轨迹点
     */
    private void processSingleTrack(GPSTrack track) {
        // 1. 查找该GPS位置附近的已知资产
        List<TrafficAsset> nearbyAssets = trafficAssetMapper.findNearbyAssets(
                track.getLatitude(), track.getLongitude(), ASSET_PROXIMITY_DISTANCE);

        if (nearbyAssets.isEmpty()) {
            log.debug("GPS位置 ({}, {}) 附近没有已知资产", track.getLatitude(), track.getLongitude());
            return;
        }

        log.debug("GPS位置 ({}, {}) 附近发现 {} 个已知资产", 
                track.getLatitude(), track.getLongitude(), nearbyAssets.size());

        // 2. 检查在该时间点附近是否有对应的检测记录
        LocalDateTime trackTime = track.getCreatedTime();
        LocalDateTime startTime = trackTime.minusMinutes(DETECTION_TIME_WINDOW_MINUTES);
        LocalDateTime endTime = trackTime.plusMinutes(DETECTION_TIME_WINDOW_MINUTES);

        List<DetectionRecord> nearbyDetections = detectionRecordMapper.findByTimeRange(startTime, endTime);

        // 3. 过滤出在相同位置的检测记录
        List<DetectionRecord> sameLocationDetections = nearbyDetections.stream()
                .filter(detection -> isInSameLocation(detection, track))
                .collect(Collectors.toList());

        // 4. 分析资产缺失情况
        analyzeAssetMissing(nearbyAssets, sameLocationDetections, track);
    }

    /**
     * 判断检测记录是否在相同位置
     */
    private boolean isInSameLocation(DetectionRecord detection, GPSTrack track) {
        if (detection.getGpsLatitude() == null || detection.getGpsLongitude() == null) {
            return false;
        }

        double distance = calculateDistance(
                detection.getGpsLatitude(), detection.getGpsLongitude(),
                track.getLatitude(), track.getLongitude());

        return distance <= ASSET_PROXIMITY_DISTANCE;
    }

    /**
     * 分析资产缺失情况
     */
    private void analyzeAssetMissing(List<TrafficAsset> nearbyAssets, 
                                   List<DetectionRecord> sameLocationDetections, 
                                   GPSTrack track) {
        
        // 获取检测到的资产类型和名称
        Set<String> detectedAssetTypes = sameLocationDetections.stream()
                .flatMap(detection -> detection.getDetectionDetails().stream())
                .map(detail -> detail.getType() + ":" + detail.getName())
                .collect(Collectors.toSet());

        // 检查每个已知资产是否被检测到
        for (TrafficAsset asset : nearbyAssets) {
            String assetKey = asset.getType() + ":" + asset.getName();
            
            if (!detectedAssetTypes.contains(assetKey)) {
                // 资产缺失！
                handleMissingAsset(asset, track);
            } else {
                // 资产正常检测到，更新最后检测时间
                updateAssetLastDetected(asset);
            }
        }
    }

    /**
     * 处理缺失的资产
     */
    private void handleMissingAsset(TrafficAsset asset, GPSTrack track) {
        log.warn("检测到资产缺失: 资产ID={}, 类型={}, 名称={}, GPS位置=({}, {})", 
                asset.getAssetId(), asset.getType(), asset.getName(), 
                track.getLatitude(), track.getLongitude());

        // 检查资产当前状态，避免重复报警
        if (!"MISSING".equals(asset.getStatus())) {
            // 更新资产状态为缺失
            asset.setStatus("MISSING");
            asset.setUpdatedTime(LocalDateTime.now());
            trafficAssetMapper.updateAsset(asset);

            // 发送缺失报警
            assetAlertService.sendMissingAssetAlert(asset);
            
            log.info("资产已标记为缺失并发送报警: {}", asset.getAssetId());
        } else {
            log.debug("资产已处于MISSING状态，跳过重复报警: {}", asset.getAssetId());
        }
    }

    /**
     * 更新资产最后检测时间
     */
    private void updateAssetLastDetected(TrafficAsset asset) {
        if (!"ACTIVE".equals(asset.getStatus())) {
            // 如果资产之前是MISSING状态，现在检测到了，恢复为ACTIVE
            asset.setStatus("ACTIVE");
            log.info("资产状态从MISSING恢复为ACTIVE: {}", asset.getAssetId());
        }
        
        asset.setLastDetectedTime(LocalDateTime.now());
        asset.setDetectionCount(asset.getDetectionCount() + 1);
        asset.setUpdatedTime(LocalDateTime.now());
        trafficAssetMapper.updateAsset(asset);
    }

    /**
     * 计算两点间距离（米）
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }

    /**
     * 手动触发指定区域的资产缺失检测
     */
    public void triggerMissingDetectionForArea(double latitude, double longitude, double radius) {
        log.info("手动触发区域资产缺失检测: 中心({}, {}), 半径{}米", latitude, longitude, radius);
        
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(1); // 检查最近1小时的轨迹
        
        List<GPSTrack> nearbyTracks = gpsTrackMapper.findNearbyTracks(
                latitude, longitude, radius, startTime, endTime);
        
        if (!nearbyTracks.isEmpty()) {
            processGPSTracks(nearbyTracks);
        } else {
            log.info("指定区域内没有找到GPS轨迹记录");
        }
    }
}
