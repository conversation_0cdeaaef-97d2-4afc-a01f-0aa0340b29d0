package com.ybda.service;

import com.ybda.dto.GPSDataDTO;
import com.ybda.entity.DetectionDetail;
import com.ybda.entity.DetectionRecord;
import com.ybda.entity.TrafficAsset;
import com.ybda.repository.TrafficAssetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 数据融合服务
 * 将GPS数据与检测数据进行融合，生成交通资产信息
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataFusionService {

    private final TrafficAssetRepository trafficAssetRepository;
    private final GPSService gpsService;

    // 资产融合的距离阈值（米）
    private static final double ASSET_MERGE_DISTANCE = 10.0;

    /**
     * 处理检测记录，进行数据融合
     * @param detectionRecord 检测记录
     */
    @Transactional
    public void processDetectionRecord(DetectionRecord detectionRecord) {
        log.info("开始处理检测记录，帧ID: {}", detectionRecord.getFrameId());

        try {
            // 获取GPS数据
            GPSDataDTO gpsData = gpsService.getCurrentGPSData();
            if (gpsData == null || !gpsService.isValidGPSData(gpsData)) {
                log.warn("无效的GPS数据，跳过处理，帧ID: {}", detectionRecord.getFrameId());
                return;
            }

            // 更新检测记录的GPS信息
            detectionRecord.setGpsLatitude(gpsData.getLatitude());
            detectionRecord.setGpsLongitude(gpsData.getLongitude());

            // 处理每个检测详情
            for (DetectionDetail detail : detectionRecord.getDetectionDetails()) {
                processDetectionDetail(detail, gpsData);
            }

            // 更新处理状态
            detectionRecord.setProcessStatus("PROCESSED");
            detectionRecord.setProcessedTime(LocalDateTime.now());

            log.info("检测记录处理完成，帧ID: {}", detectionRecord.getFrameId());

        } catch (Exception e) {
            log.error("处理检测记录失败，帧ID: {}", detectionRecord.getFrameId(), e);
            detectionRecord.setProcessStatus("FAILED");
            detectionRecord.setProcessedTime(LocalDateTime.now());
        }
    }

    /**
     * 处理单个检测详情
     * @param detail 检测详情
     * @param gpsData GPS数据
     */
    private void processDetectionDetail(DetectionDetail detail, GPSDataDTO gpsData) {
        // 查找附近是否已存在相同类型的资产
        List<TrafficAsset> nearbyAssets = trafficAssetRepository.findNearbyAssets(
                gpsData.getLatitude(),
                gpsData.getLongitude(),
                ASSET_MERGE_DISTANCE
        );

        TrafficAsset matchedAsset = findMatchingAsset(nearbyAssets, detail);

        if (matchedAsset != null) {
            // 更新现有资产
            updateExistingAsset(matchedAsset, detail);
            detail.setTrafficAssetId(matchedAsset.getAssetId());
            log.debug("更新现有资产: {}", matchedAsset.getAssetId());
        } else {
            // 创建新资产
            TrafficAsset newAsset = createNewAsset(detail, gpsData);
            trafficAssetRepository.save(newAsset);
            detail.setTrafficAssetId(newAsset.getAssetId());
            log.debug("创建新资产: {}", newAsset.getAssetId());
        }
    }

    /**
     * 查找匹配的资产
     * @param nearbyAssets 附近的资产列表
     * @param detail 检测详情
     * @return 匹配的资产，如果没有匹配返回null
     */
    private TrafficAsset findMatchingAsset(List<TrafficAsset> nearbyAssets, DetectionDetail detail) {
        return nearbyAssets.stream()
                .filter(asset -> asset.getType().equals(detail.getType()) 
                        && asset.getName().equals(detail.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 更新现有资产
     * @param asset 现有资产
     * @param detail 检测详情
     */
    private void updateExistingAsset(TrafficAsset asset, DetectionDetail detail) {
        asset.setLastDetectedTime(LocalDateTime.now());
        asset.setDetectionCount(asset.getDetectionCount() + 1);
        asset.setStatus("ACTIVE");
        trafficAssetRepository.save(asset);
    }

    /**
     * 创建新资产
     * @param detail 检测详情
     * @param gpsData GPS数据
     * @return 新创建的资产
     */
    private TrafficAsset createNewAsset(DetectionDetail detail, GPSDataDTO gpsData) {
        LocalDateTime now = LocalDateTime.now();
        
        return TrafficAsset.builder()
                .assetId(generateAssetId())
                .type(detail.getType())
                .name(detail.getName())
                .latitude(gpsData.getLatitude())
                .longitude(gpsData.getLongitude())
                .firstDetectedTime(now)
                .lastDetectedTime(now)
                .detectionCount(1)
                .status("ACTIVE")
                .build();
    }

    /**
     * 生成资产ID
     * @return 唯一的资产ID
     */
    private String generateAssetId() {
        return "ASSET_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }
}
