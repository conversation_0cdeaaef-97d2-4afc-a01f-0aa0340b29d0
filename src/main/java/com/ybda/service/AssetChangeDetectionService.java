package com.ybda.service;

import com.ybda.dto.GPSDataDTO;
import com.ybda.entity.DetectionDetail;
import com.ybda.entity.DetectionRecord;
import com.ybda.entity.TrafficAsset;
import com.ybda.mapper.TrafficAssetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产变化检测服务
 * 核心业务逻辑：检测同一位置的资产增减变化
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetChangeDetectionService {

    private final TrafficAssetMapper trafficAssetMapper;
    private final GPSService gpsService;
    private final AssetAlertService assetAlertService;

    // 资产位置匹配的距离阈值（米）
    private static final double LOCATION_MATCH_DISTANCE = 10.0;

    /**
     * 处理检测记录，进行资产变化检测
     * @param detectionRecord 检测记录
     */
    @Transactional
    public void processAssetChangeDetection(DetectionRecord detectionRecord) {
        log.info("开始资产变化检测，帧ID: {}", detectionRecord.getFrameId());

        try {
            // 1. 获取GPS数据
            GPSDataDTO gpsData = gpsService.getCurrentGPSData();
            if (gpsData == null || !gpsService.isValidGPSData(gpsData)) {
                log.warn("无效的GPS数据，跳过处理，帧ID: {}", detectionRecord.getFrameId());
                return;
            }

            // 2. 更新检测记录的GPS信息
            detectionRecord.setGpsLatitude(gpsData.getLatitude());
            detectionRecord.setGpsLongitude(gpsData.getLongitude());

            // 3. 获取当前位置附近的历史资产
            List<TrafficAsset> nearbyHistoryAssets = trafficAssetMapper.findNearbyAssets(
                    gpsData.getLatitude(), gpsData.getLongitude(), LOCATION_MATCH_DISTANCE);

            // 4. 获取当前检测到的资产信息
            Map<String, List<DetectionDetail>> currentDetectedAssets = groupDetectionsByTypeAndName(
                    detectionRecord.getDetectionDetails());

            // 5. 进行资产变化分析
            AssetChangeResult changeResult = analyzeAssetChanges(nearbyHistoryAssets, currentDetectedAssets);

            // 6. 处理资产变化
            handleAssetChanges(changeResult, gpsData);

            // 7. 更新处理状态
            detectionRecord.setProcessStatus("PROCESSED");
            detectionRecord.setProcessedTime(LocalDateTime.now());

            log.info("资产变化检测完成，帧ID: {}, 新增: {}, 缺失: {}", 
                    detectionRecord.getFrameId(), 
                    changeResult.getNewAssets().size(), 
                    changeResult.getMissingAssets().size());

        } catch (Exception e) {
            log.error("资产变化检测失败，帧ID: {}", detectionRecord.getFrameId(), e);
            detectionRecord.setProcessStatus("FAILED");
            detectionRecord.setProcessedTime(LocalDateTime.now());
        }
    }

    /**
     * 将检测详情按类型和名称分组
     */
    private Map<String, List<DetectionDetail>> groupDetectionsByTypeAndName(List<DetectionDetail> detectionDetails) {
        return detectionDetails.stream()
                .collect(Collectors.groupingBy(detail -> detail.getType() + ":" + detail.getName()));
    }

    /**
     * 分析资产变化
     * @param historyAssets 历史资产列表
     * @param currentDetections 当前检测到的资产
     * @return 变化分析结果
     */
    private AssetChangeResult analyzeAssetChanges(List<TrafficAsset> historyAssets, 
                                                 Map<String, List<DetectionDetail>> currentDetections) {
        
        AssetChangeResult result = new AssetChangeResult();
        
        // 将历史资产按类型和名称分组
        Map<String, List<TrafficAsset>> historyAssetMap = historyAssets.stream()
                .collect(Collectors.groupingBy(asset -> asset.getType() + ":" + asset.getName()));

        // 1. 检测新增资产
        for (Map.Entry<String, List<DetectionDetail>> entry : currentDetections.entrySet()) {
            String typeAndName = entry.getKey();
            List<DetectionDetail> currentDetectionList = entry.getValue();
            List<TrafficAsset> historyAssetList = historyAssetMap.getOrDefault(typeAndName, new ArrayList<>());

            // 如果当前检测数量 > 历史数量，说明有新增
            if (currentDetectionList.size() > historyAssetList.size()) {
                int newCount = currentDetectionList.size() - historyAssetList.size();
                for (int i = 0; i < newCount; i++) {
                    result.getNewAssets().add(currentDetectionList.get(historyAssetList.size() + i));
                }
                log.info("检测到新增资产: {}, 数量: {}", typeAndName, newCount);
            }
        }

        // 2. 检测缺失资产
        for (Map.Entry<String, List<TrafficAsset>> entry : historyAssetMap.entrySet()) {
            String typeAndName = entry.getKey();
            List<TrafficAsset> historyAssetList = entry.getValue();
            List<DetectionDetail> currentDetectionList = currentDetections.getOrDefault(typeAndName, new ArrayList<>());

            // 如果历史数量 > 当前检测数量，说明有缺失
            if (historyAssetList.size() > currentDetectionList.size()) {
                int missingCount = historyAssetList.size() - currentDetectionList.size();
                for (int i = currentDetectionList.size(); i < historyAssetList.size(); i++) {
                    result.getMissingAssets().add(historyAssetList.get(i));
                }
                log.warn("检测到缺失资产: {}, 数量: {}", typeAndName, missingCount);
            }
        }

        // 3. 检测无变化的资产（需要更新最后检测时间）
        for (Map.Entry<String, List<TrafficAsset>> entry : historyAssetMap.entrySet()) {
            String typeAndName = entry.getKey();
            List<TrafficAsset> historyAssetList = entry.getValue();
            List<DetectionDetail> currentDetectionList = currentDetections.getOrDefault(typeAndName, new ArrayList<>());

            // 取较小的数量作为无变化的资产数量
            int unchangedCount = Math.min(historyAssetList.size(), currentDetectionList.size());
            for (int i = 0; i < unchangedCount; i++) {
                result.getUnchangedAssets().add(historyAssetList.get(i));
            }
        }

        return result;
    }

    /**
     * 处理资产变化
     */
    private void handleAssetChanges(AssetChangeResult changeResult, GPSDataDTO gpsData) {
        LocalDateTime now = LocalDateTime.now();

        // 1. 处理新增资产
        for (DetectionDetail newAssetDetail : changeResult.getNewAssets()) {
            TrafficAsset newAsset = createNewAsset(newAssetDetail, gpsData, now);
            trafficAssetMapper.insertAsset(newAsset);
            log.info("新增资产入库: {}", newAsset.getAssetId());
        }

        // 2. 处理缺失资产（标记为MISSING并发送报警）
        for (TrafficAsset missingAsset : changeResult.getMissingAssets()) {
            missingAsset.setStatus("MISSING");
            missingAsset.setUpdatedTime(now);
            trafficAssetMapper.updateAsset(missingAsset);
            
            // 发送缺失报警
            assetAlertService.sendMissingAssetAlert(missingAsset);
            log.warn("资产标记为缺失: {}", missingAsset.getAssetId());
        }

        // 3. 处理无变化资产（更新最后检测时间）
        for (TrafficAsset unchangedAsset : changeResult.getUnchangedAssets()) {
            unchangedAsset.setLastDetectedTime(now);
            unchangedAsset.setDetectionCount(unchangedAsset.getDetectionCount() + 1);
            unchangedAsset.setStatus("ACTIVE");
            unchangedAsset.setUpdatedTime(now);
            trafficAssetMapper.updateAsset(unchangedAsset);
        }
    }

    /**
     * 创建新资产
     */
    private TrafficAsset createNewAsset(DetectionDetail detail, GPSDataDTO gpsData, LocalDateTime now) {
        return TrafficAsset.builder()
                .assetId(generateAssetId())
                .type(detail.getType())
                .name(detail.getName())
                .latitude(gpsData.getLatitude())
                .longitude(gpsData.getLongitude())
                .firstDetectedTime(now)
                .lastDetectedTime(now)
                .detectionCount(1)
                .status("ACTIVE")
                .createdTime(now)
                .updatedTime(now)
                .build();
    }

    /**
     * 生成资产ID
     */
    private String generateAssetId() {
        return "ASSET_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }

    /**
     * 资产变化分析结果
     */
    @lombok.Data
    public static class AssetChangeResult {
        private List<DetectionDetail> newAssets = new ArrayList<>();
        private List<TrafficAsset> missingAssets = new ArrayList<>();
        private List<TrafficAsset> unchangedAssets = new ArrayList<>();
    }
}
