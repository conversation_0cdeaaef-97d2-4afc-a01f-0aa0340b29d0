package com.ybda.service;

import com.ybda.entity.TrafficAsset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 资产报警服务
 * 处理资产缺失、异常等报警信息
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AssetAlertService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 发送资产缺失报警
     * @param missingAsset 缺失的资产
     */
    public void sendMissingAssetAlert(TrafficAsset missingAsset) {
        String alertMessage = buildMissingAssetAlertMessage(missingAsset);
        
        // 打印报警信息到控制台
        log.warn("🚨 资产缺失报警 🚨");
        log.warn("报警时间: {}", LocalDateTime.now().format(FORMATTER));
        log.warn("资产ID: {}", missingAsset.getAssetId());
        log.warn("资产类型: {}", missingAsset.getType());
        log.warn("资产名称: {}", missingAsset.getName());
        log.warn("资产位置: 纬度 {}, 经度 {}", missingAsset.getLatitude(), missingAsset.getLongitude());
        log.warn("首次检测时间: {}", missingAsset.getFirstDetectedTime().format(FORMATTER));
        log.warn("最后检测时间: {}", missingAsset.getLastDetectedTime().format(FORMATTER));
        log.warn("历史检测次数: {}", missingAsset.getDetectionCount());
        log.warn("详细信息: {}", alertMessage);
        log.warn("==========================================");

        // 这里可以扩展其他报警方式：
        // - 发送邮件
        // - 发送短信
        // - 推送到监控系统
        // - 写入报警日志文件
        // - 发送到消息队列
        
        // 示例：保存到报警记录（可选）
        saveAlertRecord(missingAsset, alertMessage);
    }

    /**
     * 发送批量资产缺失报警
     * @param missingAssets 缺失的资产列表
     */
    public void sendBatchMissingAssetAlert(java.util.List<TrafficAsset> missingAssets) {
        if (missingAssets.isEmpty()) {
            return;
        }

        log.warn("🚨 批量资产缺失报警 🚨");
        log.warn("报警时间: {}", LocalDateTime.now().format(FORMATTER));
        log.warn("缺失资产总数: {}", missingAssets.size());
        log.warn("==========================================");

        for (TrafficAsset asset : missingAssets) {
            log.warn("缺失资产 - ID: {}, 类型: {}, 名称: {}, 位置: ({}, {})", 
                    asset.getAssetId(), asset.getType(), asset.getName(), 
                    asset.getLatitude(), asset.getLongitude());
        }
        log.warn("==========================================");
    }

    /**
     * 发送资产新增通知
     * @param newAsset 新增的资产
     */
    public void sendNewAssetNotification(TrafficAsset newAsset) {
        log.info("✅ 资产新增通知");
        log.info("通知时间: {}", LocalDateTime.now().format(FORMATTER));
        log.info("新增资产ID: {}", newAsset.getAssetId());
        log.info("资产类型: {}", newAsset.getType());
        log.info("资产名称: {}", newAsset.getName());
        log.info("资产位置: 纬度 {}, 经度 {}", newAsset.getLatitude(), newAsset.getLongitude());
        log.info("==========================================");
    }

    /**
     * 构建缺失资产报警消息
     */
    private String buildMissingAssetAlertMessage(TrafficAsset missingAsset) {
        StringBuilder message = new StringBuilder();
        message.append("资产缺失详情：\n");
        message.append("资产ID：").append(missingAsset.getAssetId()).append("\n");
        message.append("资产类型：").append(getAssetTypeDescription(missingAsset.getType())).append("\n");
        message.append("资产名称：").append(missingAsset.getName()).append("\n");
        message.append("资产位置：纬度 ").append(missingAsset.getLatitude())
               .append(", 经度 ").append(missingAsset.getLongitude()).append("\n");
        message.append("首次发现：").append(missingAsset.getFirstDetectedTime().format(FORMATTER)).append("\n");
        message.append("最后检测：").append(missingAsset.getLastDetectedTime().format(FORMATTER)).append("\n");
        message.append("检测次数：").append(missingAsset.getDetectionCount()).append(" 次\n");
        
        // 计算缺失时长
        long daysSinceLastDetection = java.time.Duration.between(
                missingAsset.getLastDetectedTime(), LocalDateTime.now()).toDays();
        message.append("缺失时长：约 ").append(daysSinceLastDetection).append(" 天\n");
        
        return message.toString();
    }

    /**
     * 获取资产类型描述
     */
    private String getAssetTypeDescription(String type) {
        switch (type) {
            case "ground_marking":
                return "地面标线";
            case "overhead_sign":
                return "空中标牌";
            case "traffic_light":
                return "交通信号灯";
            case "barrier":
                return "栏杆/隔离带";
            default:
                return type;
        }
    }

    /**
     * 保存报警记录（可选功能）
     * 可以扩展为保存到数据库或文件
     */
    private void saveAlertRecord(TrafficAsset missingAsset, String alertMessage) {
        // 这里可以实现报警记录的持久化
        // 例如：保存到alert_records表
        log.debug("报警记录已保存：资产ID {}", missingAsset.getAssetId());
    }

    /**
     * 发送系统状态报警
     * @param message 报警消息
     */
    public void sendSystemAlert(String message) {
        log.error("🚨 系统报警 🚨");
        log.error("报警时间: {}", LocalDateTime.now().format(FORMATTER));
        log.error("报警内容: {}", message);
        log.error("==========================================");
    }

    /**
     * 发送GPS数据异常报警
     */
    public void sendGPSDataAlert() {
        log.warn("🚨 GPS数据异常报警 🚨");
        log.warn("报警时间: {}", LocalDateTime.now().format(FORMATTER));
        log.warn("报警内容: GPS数据无效或获取失败，无法进行资产位置关联");
        log.warn("建议: 检查Redis中GPS数据是否正常更新");
        log.warn("==========================================");
    }
}
