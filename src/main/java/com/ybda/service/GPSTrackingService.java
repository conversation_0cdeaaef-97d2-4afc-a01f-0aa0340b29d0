package com.ybda.service;

import com.ybda.dto.GPSDataDTO;
import com.ybda.entity.GPSTrack;
import com.ybda.mapper.GPSTrackMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * GPS轨迹记录服务
 * 持续从Redis获取GPS数据并记录轨迹，用于后续的资产缺失检测
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GPSTrackingService {

    private final GPSService gpsService;
    private final GPSTrackMapper gpsTrackMapper;
    private final AssetMissingDetectionService assetMissingDetectionService;

    // 轨迹记录缓存，批量插入提高性能
    private final List<GPSTrack> trackBuffer = new ArrayList<>();
    private static final int BATCH_SIZE = 10;

    /**
     * 定时任务：每秒从Redis获取GPS数据并记录轨迹
     * 这是解决"POST接口只有识别到才调用"问题的关键
     */
    @Scheduled(fixedRate = 1000) // 每秒执行一次
    public void recordGPSTrack() {
        try {
            GPSDataDTO gpsData = gpsService.getCurrentGPSData();
            
            if (gpsData != null && gpsService.isValidGPSData(gpsData)) {
                GPSTrack track = createGPSTrack(gpsData);
                
                synchronized (trackBuffer) {
                    trackBuffer.add(track);
                    
                    // 批量插入，提高性能
                    if (trackBuffer.size() >= BATCH_SIZE) {
                        flushTrackBuffer();
                    }
                }
                
                log.debug("记录GPS轨迹: 纬度={}, 经度={}", gpsData.getLatitude(), gpsData.getLongitude());
            } else {
                log.warn("GPS数据无效，跳过轨迹记录");
            }
            
        } catch (Exception e) {
            log.error("记录GPS轨迹失败", e);
        }
    }

    /**
     * 定时任务：每30秒处理未处理的GPS轨迹，进行资产缺失检测
     */
    @Scheduled(fixedRate = 30000) // 每30秒执行一次
    public void processUnprocessedTracks() {
        try {
            // 先刷新缓存
            synchronized (trackBuffer) {
                if (!trackBuffer.isEmpty()) {
                    flushTrackBuffer();
                }
            }
            
            // 处理未处理的轨迹
            List<GPSTrack> unprocessedTracks = gpsTrackMapper.findUnprocessedTracks();
            
            if (!unprocessedTracks.isEmpty()) {
                log.info("开始处理未处理的GPS轨迹，数量: {}", unprocessedTracks.size());
                assetMissingDetectionService.processGPSTracks(unprocessedTracks);
            }
            
        } catch (Exception e) {
            log.error("处理GPS轨迹失败", e);
        }
    }

    /**
     * 定时任务：每天清理过期的GPS轨迹数据（保留7天）
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupOldTracks() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
            int deletedCount = gpsTrackMapper.deleteOldTracks(cutoffTime);
            log.info("清理过期GPS轨迹数据，删除记录数: {}", deletedCount);
        } catch (Exception e) {
            log.error("清理GPS轨迹数据失败", e);
        }
    }

    /**
     * 手动触发轨迹处理（用于测试或手动处理）
     */
    @Async
    public void triggerTrackProcessing() {
        log.info("手动触发GPS轨迹处理");
        processUnprocessedTracks();
    }

    /**
     * 创建GPS轨迹记录
     */
    private GPSTrack createGPSTrack(GPSDataDTO gpsData) {
        LocalDateTime now = LocalDateTime.now();
        
        return GPSTrack.builder()
                .latitude(gpsData.getLatitude())
                .longitude(gpsData.getLongitude())
                .gpsTimestamp(gpsData.getTimestamp())
                .accuracy(gpsData.getAccuracy())
                .altitude(gpsData.getAltitude())
                .speed(gpsData.getSpeed())
                .bearing(gpsData.getBearing())
                .deviceId("default") // 可以从配置中获取
                .processed(false)
                .createdTime(now)
                .build();
    }

    /**
     * 刷新轨迹缓存，批量插入数据库
     */
    private void flushTrackBuffer() {
        if (!trackBuffer.isEmpty()) {
            try {
                gpsTrackMapper.batchInsertTracks(new ArrayList<>(trackBuffer));
                log.debug("批量插入GPS轨迹记录，数量: {}", trackBuffer.size());
                trackBuffer.clear();
            } catch (Exception e) {
                log.error("批量插入GPS轨迹失败", e);
                trackBuffer.clear(); // 清空缓存避免重复插入
            }
        }
    }

    /**
     * 获取未处理轨迹数量
     */
    public long getUnprocessedTrackCount() {
        return gpsTrackMapper.countUnprocessedTracks();
    }

    /**
     * 获取指定时间范围内的轨迹
     */
    public List<GPSTrack> getTracksByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return gpsTrackMapper.findTracksByTimeRange(startTime, endTime);
    }
}
