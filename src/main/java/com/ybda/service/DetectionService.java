package com.ybda.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.dto.DetectionRequestDTO;
import com.ybda.dto.SignDetectionDTO;
import com.ybda.entity.DetectionDetail;
import com.ybda.entity.DetectionRecord;
import com.ybda.repository.DetectionRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测数据处理服务
 * 处理来自视频分析系统的检测数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DetectionService {

    private final DetectionRecordRepository detectionRecordRepository;
    private final DataFusionService dataFusionService;
    private final ObjectMapper objectMapper;

    /**
     * 处理检测请求
     * @param request 检测请求数据
     * @return 处理结果消息
     */
    @Transactional
    public String processDetectionRequest(DetectionRequestDTO request) {
        log.info("接收到检测数据，帧ID: {}, 标志数量: {}", 
                request.getFrameId(), request.getSigns().size());

        try {
            // 检查是否已存在相同帧ID的记录
            if (detectionRecordRepository.findByFrameId(request.getFrameId()).isPresent()) {
                log.warn("帧ID已存在，跳过处理: {}", request.getFrameId());
                return "帧ID已存在，跳过处理";
            }

            // 创建检测记录
            DetectionRecord detectionRecord = createDetectionRecord(request);
            
            // 创建检测详情列表
            List<DetectionDetail> detectionDetails = createDetectionDetails(request.getSigns(), detectionRecord);
            detectionRecord.setDetectionDetails(detectionDetails);

            // 保存检测记录
            detectionRecord = detectionRecordRepository.save(detectionRecord);

            // 异步进行数据融合处理
            dataFusionService.processDetectionRecord(detectionRecord);

            log.info("检测数据处理完成，帧ID: {}", request.getFrameId());
            return "检测数据处理成功";

        } catch (Exception e) {
            log.error("处理检测数据失败，帧ID: {}", request.getFrameId(), e);
            throw new RuntimeException("处理检测数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建检测记录
     * @param request 检测请求
     * @return 检测记录
     */
    private DetectionRecord createDetectionRecord(DetectionRequestDTO request) {
        try {
            String rawData = objectMapper.writeValueAsString(request);
            
            return DetectionRecord.builder()
                    .frameId(request.getFrameId())
                    .timestamp(request.getTimestamp())
                    .deviceId(request.getDeviceId())
                    .rawData(rawData)
                    .processStatus("PENDING")
                    .build();
        } catch (Exception e) {
            log.error("创建检测记录失败", e);
            throw new RuntimeException("创建检测记录失败");
        }
    }

    /**
     * 创建检测详情列表
     * @param signs 标志检测列表
     * @param detectionRecord 检测记录
     * @return 检测详情列表
     */
    private List<DetectionDetail> createDetectionDetails(List<SignDetectionDTO> signs, 
                                                        DetectionRecord detectionRecord) {
        List<DetectionDetail> details = new ArrayList<>();
        
        for (SignDetectionDTO sign : signs) {
            DetectionDetail detail = DetectionDetail.builder()
                    .detectionRecord(detectionRecord)
                    .type(sign.getType())
                    .name(sign.getName())
                    .modelSource(sign.getModelSource())
                    .classId(sign.getClassId())
                    .bboxX1(sign.getBbox().get(0))
                    .bboxY1(sign.getBbox().get(1))
                    .bboxX2(sign.getBbox().get(2))
                    .bboxY2(sign.getBbox().get(3))
                    .trackId(sign.getTrackId())
                    .build();
            
            details.add(detail);
        }
        
        return details;
    }

    /**
     * 获取待处理的检测记录数量
     * @return 待处理记录数量
     */
    public long getPendingRecordsCount() {
        return detectionRecordRepository.countByProcessStatus("PENDING");
    }

    /**
     * 获取处理失败的检测记录数量
     * @return 失败记录数量
     */
    public long getFailedRecordsCount() {
        return detectionRecordRepository.countByProcessStatus("FAILED");
    }
}
