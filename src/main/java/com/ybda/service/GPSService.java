package com.ybda.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.dto.GPSDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * GPS数据服务
 * 从Redis获取GPS位置信息
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GPSService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    private static final String GPS_KEY = "gps";

    /**
     * 从Redis获取当前GPS数据
     * @return GPS数据，如果获取失败返回null
     */
    public GPSDataDTO getCurrentGPSData() {
        try {
            Object gpsData = redisTemplate.opsForValue().get(GPS_KEY);
            if (gpsData == null) {
                log.warn("Redis中未找到GPS数据，key: {}", GPS_KEY);
                return null;
            }

            // 如果Redis中存储的是JSON字符串，需要反序列化
            if (gpsData instanceof String) {
                return objectMapper.readValue((String) gpsData, GPSDataDTO.class);
            }
            
            // 如果Redis中存储的是对象，直接转换
            return objectMapper.convertValue(gpsData, GPSDataDTO.class);
            
        } catch (Exception e) {
            log.error("从Redis获取GPS数据失败", e);
            return null;
        }
    }

    /**
     * 检查GPS数据是否有效
     * @param gpsData GPS数据
     * @return 是否有效
     */
    public boolean isValidGPSData(GPSDataDTO gpsData) {
        if (gpsData == null) {
            return false;
        }
        
        Double latitude = gpsData.getLatitude();
        Double longitude = gpsData.getLongitude();
        
        // 检查经纬度是否在有效范围内
        return latitude != null && longitude != null
                && latitude >= -90 && latitude <= 90
                && longitude >= -180 && longitude <= 180;
    }

    /**
     * 计算两个GPS坐标之间的距离（米）
     * 使用Haversine公式
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 距离（米）
     */
    public double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
}
