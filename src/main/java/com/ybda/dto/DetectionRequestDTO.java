package com.ybda.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 视频分析检测请求DTO
 * 接收来自视频分析系统的POST请求数据
 */
@Data
public class DetectionRequestDTO {

    /**
     * 帧ID，用于标识检测帧
     */
    @NotBlank(message = "帧ID不能为空")
    private String frameId;

    /**
     * Unix时间戳，检测时间
     */
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;

    /**
     * 检测到的交通标志列表
     */
    @NotEmpty(message = "检测标志列表不能为空")
    @Valid
    private List<SignDetectionDTO> signs;

    /**
     * 设备标识符（可选）
     */
    private String deviceId;
}
