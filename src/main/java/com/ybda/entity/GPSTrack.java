package com.ybda.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * GPS轨迹记录实体类
 * 持续记录车辆的GPS轨迹，用于资产缺失检测
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GPSTrack {

    private Long id;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * GPS时间戳
     */
    private Long gpsTimestamp;

    /**
     * GPS精度
     */
    private Double accuracy;

    /**
     * 海拔高度
     */
    private Double altitude;

    /**
     * 速度（km/h）
     */
    private Double speed;

    /**
     * 方向角
     */
    private Double bearing;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 是否已处理（用于资产匹配检测）
     */
    private Boolean processed;

    /**
     * 当前范围内的资产ID列表（JSON格式存储，用于状态机判断）
     * 例如: ["ASSET_001", "ASSET_002"]
     */
    private String currentNearbyAssets;

    /**
     * 记录时间
     */
    private LocalDateTime createdTime;
}
