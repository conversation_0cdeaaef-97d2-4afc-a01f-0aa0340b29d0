package com.ybda.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 检测详情实体类
 * 存储每个检测到的交通标志的详细信息
 */
@Entity
@Table(name = "detection_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectionDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的检测记录
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "detection_record_id", nullable = false)
    private DetectionRecord detectionRecord;

    /**
     * 标志类型：ground_marking, overhead_sign, traffic_light, barrier
     */
    @Column(name = "type", nullable = false, length = 32)
    private String type;

    /**
     * 标志名称，如"限速标志"、"禁止通行"
     */
    @Column(name = "name", nullable = false, length = 128)
    private String name;

    /**
     * 模型推理路径，如"#1→#7→#4"
     */
    @Column(name = "model_source", length = 256)
    private String modelSource;

    /**
     * 标志分类ID
     */
    @Column(name = "class_id", nullable = false)
    private Integer classId;

    /**
     * 边界框坐标 x1
     */
    @Column(name = "bbox_x1", nullable = false)
    private Double bboxX1;

    /**
     * 边界框坐标 y1
     */
    @Column(name = "bbox_y1", nullable = false)
    private Double bboxY1;

    /**
     * 边界框坐标 x2
     */
    @Column(name = "bbox_x2", nullable = false)
    private Double bboxX2;

    /**
     * 边界框坐标 y2
     */
    @Column(name = "bbox_y2", nullable = false)
    private Double bboxY2;

    /**
     * 跟踪ID，同一物体的唯一标识
     */
    @Column(name = "track_id", nullable = false)
    private Integer trackId;

    /**
     * 关联的交通资产ID（融合处理后）
     */
    @Column(name = "traffic_asset_id", length = 64)
    private String trafficAssetId;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
    }
}
