package com.ybda.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 检测详情实体类
 * 存储每个检测到的交通标志的详细信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectionDetail {

    private Long id;

    /**
     * 关联的检测记录ID
     */
    private Long detectionRecordId;

    /**
     * 标志类型：ground_marking, overhead_sign, traffic_light, barrier
     */
    private String type;

    /**
     * 标志名称，如"限速标志"、"禁止通行"
     */
    private String name;

    /**
     * 模型推理路径，如"#1→#7→#4"
     */
    private String modelSource;

    /**
     * 标志分类ID
     */
    private Integer classId;

    /**
     * 边界框坐标 x1
     */
    private Double bboxX1;

    /**
     * 边界框坐标 y1
     */
    private Double bboxY1;

    /**
     * 边界框坐标 x2
     */
    private Double bboxX2;

    /**
     * 边界框坐标 y2
     */
    private Double bboxY2;

    /**
     * 跟踪ID，同一物体的唯一标识
     */
    private Integer trackId;

    /**
     * 关联的交通资产ID（融合处理后）
     */
    private String trafficAssetId;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
