package com.ybda.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 交通资产实体类
 * 存储经过融合处理后的交通资产信息
 */
@Entity
@Table(name = "traffic_assets")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TrafficAsset {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 资产唯一标识符
     */
    @Column(name = "asset_id", unique = true, nullable = false, length = 64)
    private String assetId;

    /**
     * 资产类型：ground_marking, overhead_sign, traffic_light, barrier
     */
    @Column(name = "type", nullable = false, length = 32)
    private String type;

    /**
     * 资产名称，如"限速标志"、"禁止通行"
     */
    @Column(name = "name", nullable = false, length = 128)
    private String name;

    /**
     * 资产所在纬度
     */
    @Column(name = "latitude", nullable = false, precision = 10, scale = 8)
    private Double latitude;

    /**
     * 资产所在经度
     */
    @Column(name = "longitude", nullable = false, precision = 11, scale = 8)
    private Double longitude;

    /**
     * 首次检测到该资产的时间
     */
    @Column(name = "first_detected_time", nullable = false)
    private LocalDateTime firstDetectedTime;

    /**
     * 最后一次检测到该资产的时间
     */
    @Column(name = "last_detected_time", nullable = false)
    private LocalDateTime lastDetectedTime;

    /**
     * 累计检测次数
     */
    @Column(name = "detection_count", nullable = false)
    private Integer detectionCount;

    /**
     * 资产状态：ACTIVE-活跃, INACTIVE-不活跃
     */
    @Column(name = "status", nullable = false, length = 16)
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdTime = now;
        this.updatedTime = now;
        if (this.status == null) {
            this.status = "ACTIVE";
        }
        if (this.detectionCount == null) {
            this.detectionCount = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedTime = LocalDateTime.now();
    }
}
