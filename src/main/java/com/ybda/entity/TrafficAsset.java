package com.ybda.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 交通资产实体类
 * 存储经过融合处理后的交通资产信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TrafficAsset {

    private Long id;

    /**
     * 资产唯一标识符
     */
    private String assetId;

    /**
     * 资产类型：ground_marking, overhead_sign, traffic_light, barrier
     */
    private String type;

    /**
     * 资产名称，如"限速标志"、"禁止通行"
     */
    private String name;

    /**
     * 资产所在纬度
     */
    private Double latitude;

    /**
     * 资产所在经度
     */
    private Double longitude;

    /**
     * 首次检测到该资产的时间
     */
    private LocalDateTime firstDetectedTime;

    /**
     * 最后一次检测到该资产的时间
     */
    private LocalDateTime lastDetectedTime;

    /**
     * 累计检测次数
     */
    private Integer detectionCount;

    /**
     * 资产状态：ACTIVE-活跃, MISSING-缺失, INACTIVE-不活跃
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
