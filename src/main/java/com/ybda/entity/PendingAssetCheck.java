package com.ybda.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 待确认资产检查记录
 * 当GPS经过资产位置时创建，等待2分钟后验证是否收到POST请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PendingAssetCheck {

    private Long id;

    /**
     * 关联的GPS轨迹ID
     */
    private Long gpsTrackId;

    /**
     * 预期的资产ID
     */
    private String expectedAssetId;

    /**
     * 资产类型
     */
    private String assetType;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * GPS位置（经过资产位置的坐标）
     */
    private Double gpsLatitude;
    private Double gpsLongitude;

    /**
     * 经过时间（GPS轨迹时间）
     */
    private LocalDateTime passedTime;

    /**
     * 检查状态：PENDING-待检查, CONFIRMED-已确认, MISSING-缺失
     */
    private String status;

    /**
     * 确认的检测记录ID（如果收到POST请求）
     */
    private Long confirmedDetectionId;

    /**
     * 确认时间
     */
    private LocalDateTime confirmedTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
