package com.ybda.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 检测记录实体类
 * 存储每次视频分析的原始检测数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectionRecord {

    private Long id;

    /**
     * 帧ID，用于标识检测帧（每次都不同）
     */
    private String frameId;

    /**
     * Unix时间戳，检测时间
     */
    private Long timestamp;

    /**
     * GPS纬度（从Redis获取）
     */
    private Double gpsLatitude;

    /**
     * GPS经度（从Redis获取）
     */
    private Double gpsLongitude;

    /**
     * 设备标识符
     */
    private String deviceId;

    /**
     * 处理状态：PENDING-待处理, PROCESSED-已处理, FAILED-处理失败
     */
    private String processStatus;

    /**
     * 原始JSON数据
     */
    private String rawData;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 处理时间
     */
    private LocalDateTime processedTime;

    /**
     * 检测详情列表
     */
    private List<DetectionDetail> detectionDetails;
}
