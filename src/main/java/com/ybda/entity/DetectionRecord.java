package com.ybda.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 检测记录实体类
 * 存储每次视频分析的原始检测数据
 */
@Entity
@Table(name = "detection_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DetectionRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 帧ID，用于标识检测帧
     */
    @Column(name = "frame_id", nullable = false, length = 64)
    private String frameId;

    /**
     * Unix时间戳，检测时间
     */
    @Column(name = "timestamp", nullable = false)
    private Long timestamp;

    /**
     * GPS纬度（从Redis获取）
     */
    @Column(name = "gps_latitude", precision = 10, scale = 8)
    private Double gpsLatitude;

    /**
     * GPS经度（从Redis获取）
     */
    @Column(name = "gps_longitude", precision = 11, scale = 8)
    private Double gpsLongitude;

    /**
     * 设备标识符
     */
    @Column(name = "device_id", length = 64)
    private String deviceId;

    /**
     * 处理状态：PENDING-待处理, PROCESSED-已处理, FAILED-处理失败
     */
    @Column(name = "process_status", nullable = false, length = 16)
    private String processStatus;

    /**
     * 原始JSON数据
     */
    @Column(name = "raw_data", columnDefinition = "TEXT")
    private String rawData;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    /**
     * 处理时间
     */
    @Column(name = "processed_time")
    private LocalDateTime processedTime;

    /**
     * 检测详情列表
     */
    @OneToMany(mappedBy = "detectionRecord", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DetectionDetail> detectionDetails;

    @PrePersist
    protected void onCreate() {
        this.createdTime = LocalDateTime.now();
        if (this.processStatus == null) {
            this.processStatus = "PENDING";
        }
    }
}
