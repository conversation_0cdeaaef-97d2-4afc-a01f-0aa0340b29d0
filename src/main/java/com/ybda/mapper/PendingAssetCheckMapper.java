package com.ybda.mapper;

import com.ybda.entity.PendingAssetCheck;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 待确认资产检查Mapper接口
 */
@Mapper
public interface PendingAssetCheckMapper {

    /**
     * 插入待确认记录
     */
    @Insert("INSERT INTO pending_asset_checks (gps_track_id, expected_asset_id, asset_type, asset_name, " +
            "gps_latitude, gps_longitude, passed_time, status, created_time, updated_time) " +
            "VALUES (#{gpsTrackId}, #{expectedAssetId}, #{assetType}, #{assetName}, " +
            "#{gpsLatitude}, #{gpsLongitude}, #{passedTime}, #{status}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertPendingCheck(PendingAssetCheck pendingCheck);

    /**
     * 批量插入待确认记录
     */
    @Insert("<script>" +
            "INSERT INTO pending_asset_checks (gps_track_id, expected_asset_id, asset_type, asset_name, " +
            "gps_latitude, gps_longitude, passed_time, status, created_time, updated_time) VALUES " +
            "<foreach collection='checks' item='check' separator=','>" +
            "(#{check.gpsTrackId}, #{check.expectedAssetId}, #{check.assetType}, #{check.assetName}, " +
            "#{check.gpsLatitude}, #{check.gpsLongitude}, #{check.passedTime}, #{check.status}, " +
            "#{check.createdTime}, #{check.updatedTime})" +
            "</foreach>" +
            "</script>")
    int batchInsertPendingChecks(@Param("checks") List<PendingAssetCheck> checks);

    /**
     * 更新确认状态
     */
    @Update("UPDATE pending_asset_checks SET status = #{status}, confirmed_detection_id = #{confirmedDetectionId}, " +
            "confirmed_time = #{confirmedTime}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updateConfirmStatus(PendingAssetCheck pendingCheck);

    /**
     * 根据状态查找记录
     */
    @Select("SELECT * FROM pending_asset_checks WHERE status = #{status}")
    List<PendingAssetCheck> findByStatus(String status);

    /**
     * 查找超时的待确认记录（2分钟前创建的PENDING记录）
     */
    @Select("SELECT * FROM pending_asset_checks WHERE status = 'PENDING' " +
            "AND passed_time <= #{timeoutBefore}")
    List<PendingAssetCheck> findTimeoutPendingChecks(@Param("timeoutBefore") LocalDateTime timeoutBefore);

    /**
     * 查找指定位置和时间范围内的待确认记录
     */
    @Select("SELECT * FROM pending_asset_checks WHERE " +
            "(6371000 * acos(cos(radians(#{latitude})) * cos(radians(gps_latitude)) * " +
            "cos(radians(gps_longitude) - radians(#{longitude})) + " +
            "sin(radians(#{latitude})) * sin(radians(gps_latitude)))) <= #{distance} " +
            "AND passed_time BETWEEN #{startTime} AND #{endTime}")
    List<PendingAssetCheck> findNearbyPendingChecks(@Param("latitude") Double latitude,
                                                    @Param("longitude") Double longitude,
                                                    @Param("distance") Double distance,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 根据资产ID和时间范围查找待确认记录
     */
    @Select("SELECT * FROM pending_asset_checks WHERE expected_asset_id = #{assetId} " +
            "AND passed_time BETWEEN #{startTime} AND #{endTime} " +
            "AND status = 'PENDING'")
    List<PendingAssetCheck> findPendingChecksByAssetAndTime(@Param("assetId") String assetId,
                                                           @Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 删除过期的记录（清理历史数据）
     */
    @Delete("DELETE FROM pending_asset_checks WHERE created_time < #{beforeTime}")
    int deleteOldChecks(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计各状态的记录数量
     */
    @Select("SELECT status, COUNT(*) as count FROM pending_asset_checks GROUP BY status")
    List<Object[]> countByStatus();

    /**
     * 根据ID查找记录
     */
    @Select("SELECT * FROM pending_asset_checks WHERE id = #{id}")
    PendingAssetCheck findById(Long id);
}
