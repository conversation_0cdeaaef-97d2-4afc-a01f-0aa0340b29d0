package com.ybda.mapper;

import com.ybda.entity.TrafficAsset;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交通资产Mapper接口
 */
@Mapper
public interface TrafficAssetMapper {

    /**
     * 插入新资产
     */
    @Insert("INSERT INTO traffic_assets (asset_id, type, name, latitude, longitude, " +
            "first_detected_time, last_detected_time, detection_count, status, created_time, updated_time) " +
            "VALUES (#{assetId}, #{type}, #{name}, #{latitude}, #{longitude}, " +
            "#{firstDetectedTime}, #{lastDetectedTime}, #{detectionCount}, #{status}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertAsset(TrafficAsset asset);

    /**
     * 更新资产信息
     */
    @Update("UPDATE traffic_assets SET last_detected_time = #{lastDetectedTime}, " +
            "detection_count = #{detectionCount}, status = #{status}, updated_time = #{updatedTime} " +
            "WHERE id = #{id}")
    int updateAsset(TrafficAsset asset);

    /**
     * 根据ID查找资产
     */
    @Select("SELECT * FROM traffic_assets WHERE id = #{id}")
    TrafficAsset findById(Long id);

    /**
     * 根据资产ID查找
     */
    @Select("SELECT * FROM traffic_assets WHERE asset_id = #{assetId}")
    TrafficAsset findByAssetId(String assetId);

    /**
     * 查找指定位置附近的资产（用于资产变化检测）
     * 使用地理距离计算，查找指定范围内的资产
     */
    @Select("SELECT * FROM traffic_assets WHERE " +
            "(6371000 * acos(cos(radians(#{latitude})) * cos(radians(latitude)) * " +
            "cos(radians(longitude) - radians(#{longitude})) + " +
            "sin(radians(#{latitude})) * sin(radians(latitude)))) <= #{distance} " +
            "AND status != 'MISSING'")
    List<TrafficAsset> findNearbyAssets(@Param("latitude") Double latitude,
                                       @Param("longitude") Double longitude,
                                       @Param("distance") Double distance);

    /**
     * 根据类型查找资产
     */
    @Select("SELECT * FROM traffic_assets WHERE type = #{type}")
    List<TrafficAsset> findByType(String type);

    /**
     * 根据状态查找资产
     */
    @Select("SELECT * FROM traffic_assets WHERE status = #{status}")
    List<TrafficAsset> findByStatus(String status);

    /**
     * 在指定地理范围内查找资产
     */
    @Select("SELECT * FROM traffic_assets WHERE latitude BETWEEN #{minLat} AND #{maxLat} " +
            "AND longitude BETWEEN #{minLng} AND #{maxLng}")
    List<TrafficAsset> findAssetsInBounds(@Param("minLat") Double minLat,
                                         @Param("maxLat") Double maxLat,
                                         @Param("minLng") Double minLng,
                                         @Param("maxLng") Double maxLng);

    /**
     * 统计资产总数
     */
    @Select("SELECT COUNT(*) FROM traffic_assets")
    long countAll();

    /**
     * 根据状态统计资产数量
     */
    @Select("SELECT COUNT(*) FROM traffic_assets WHERE status = #{status}")
    long countByStatus(String status);

    /**
     * 批量更新资产状态为MISSING（用于资产缺失检测）
     */
    @Update("UPDATE traffic_assets SET status = 'MISSING', updated_time = #{updateTime} " +
            "WHERE id IN (${assetIds})")
    int batchUpdateStatusToMissing(@Param("assetIds") String assetIds, 
                                  @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取所有资产（分页）
     */
    @Select("SELECT * FROM traffic_assets ORDER BY last_detected_time DESC LIMIT #{offset}, #{limit}")
    List<TrafficAsset> findAllWithPaging(@Param("offset") int offset, @Param("limit") int limit);
}
