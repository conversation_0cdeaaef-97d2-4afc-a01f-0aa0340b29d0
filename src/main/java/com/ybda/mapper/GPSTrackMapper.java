package com.ybda.mapper;

import com.ybda.entity.GPSTrack;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * GPS轨迹Mapper接口
 */
@Mapper
public interface GPSTrackMapper {

    /**
     * 插入GPS轨迹记录
     */
    @Insert("INSERT INTO gps_tracks (latitude, longitude, gps_timestamp, accuracy, altitude, " +
            "speed, bearing, device_id, processed, created_time) " +
            "VALUES (#{latitude}, #{longitude}, #{gpsTimestamp}, #{accuracy}, #{altitude}, " +
            "#{speed}, #{bearing}, #{deviceId}, #{processed}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertTrack(GPSTrack track);

    /**
     * 批量插入GPS轨迹记录
     */
    @Insert("<script>" +
            "INSERT INTO gps_tracks (latitude, longitude, gps_timestamp, accuracy, altitude, " +
            "speed, bearing, device_id, processed, created_time) VALUES " +
            "<foreach collection='tracks' item='track' separator=','>" +
            "(#{track.latitude}, #{track.longitude}, #{track.gpsTimestamp}, #{track.accuracy}, " +
            "#{track.altitude}, #{track.speed}, #{track.bearing}, #{track.deviceId}, " +
            "#{track.processed}, #{track.createdTime})" +
            "</foreach>" +
            "</script>")
    int batchInsertTracks(@Param("tracks") List<GPSTrack> tracks);

    /**
     * 更新处理状态
     */
    @Update("UPDATE gps_tracks SET processed = #{processed} WHERE id = #{id}")
    int updateProcessedStatus(@Param("id") Long id, @Param("processed") Boolean processed);

    /**
     * 查找未处理的GPS轨迹记录
     */
    @Select("SELECT * FROM gps_tracks WHERE processed = false ORDER BY created_time ASC")
    List<GPSTrack> findUnprocessedTracks();

    /**
     * 查找指定时间范围内的GPS轨迹
     */
    @Select("SELECT * FROM gps_tracks WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY created_time ASC")
    List<GPSTrack> findTracksByTimeRange(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查找指定位置附近的GPS轨迹记录
     */
    @Select("SELECT * FROM gps_tracks WHERE " +
            "(6371000 * acos(cos(radians(#{latitude})) * cos(radians(latitude)) * " +
            "cos(radians(longitude) - radians(#{longitude})) + " +
            "sin(radians(#{latitude})) * sin(radians(latitude)))) <= #{distance} " +
            "AND created_time BETWEEN #{startTime} AND #{endTime}")
    List<GPSTrack> findNearbyTracks(@Param("latitude") Double latitude,
                                   @Param("longitude") Double longitude,
                                   @Param("distance") Double distance,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 删除过期的GPS轨迹记录（清理历史数据）
     */
    @Delete("DELETE FROM gps_tracks WHERE created_time < #{beforeTime}")
    int deleteOldTracks(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计未处理的轨迹数量
     */
    @Select("SELECT COUNT(*) FROM gps_tracks WHERE processed = false")
    long countUnprocessedTracks();

    /**
     * 根据设备ID查找轨迹
     */
    @Select("SELECT * FROM gps_tracks WHERE device_id = #{deviceId} " +
            "ORDER BY created_time DESC LIMIT #{limit}")
    List<GPSTrack> findTracksByDeviceId(@Param("deviceId") String deviceId, 
                                       @Param("limit") int limit);
}
