package com.ybda.mapper;

import com.ybda.entity.DetectionRecord;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 检测记录Mapper接口
 */
@Mapper
public interface DetectionRecordMapper {

    /**
     * 插入检测记录
     */
    @Insert("INSERT INTO detection_records (frame_id, timestamp, gps_latitude, gps_longitude, " +
            "device_id, process_status, raw_data, created_time) " +
            "VALUES (#{frameId}, #{timestamp}, #{gpsLatitude}, #{gpsLongitude}, " +
            "#{deviceId}, #{processStatus}, #{rawData}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertRecord(DetectionRecord record);

    /**
     * 更新处理状态
     */
    @Update("UPDATE detection_records SET process_status = #{processStatus}, " +
            "processed_time = #{processedTime} WHERE id = #{id}")
    int updateProcessStatus(DetectionRecord record);

    /**
     * 根据ID查找记录
     */
    @Select("SELECT * FROM detection_records WHERE id = #{id}")
    DetectionRecord findById(Long id);

    /**
     * 根据处理状态查找记录
     */
    @Select("SELECT * FROM detection_records WHERE process_status = #{processStatus}")
    List<DetectionRecord> findByProcessStatus(String processStatus);

    /**
     * 根据设备ID查找记录
     */
    @Select("SELECT * FROM detection_records WHERE device_id = #{deviceId}")
    List<DetectionRecord> findByDeviceId(String deviceId);

    /**
     * 查找指定时间范围内的记录
     */
    @Select("SELECT * FROM detection_records WHERE created_time BETWEEN #{startTime} AND #{endTime}")
    List<DetectionRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查找待处理的记录
     */
    @Select("SELECT * FROM detection_records WHERE process_status = 'PENDING' ORDER BY created_time ASC")
    List<DetectionRecord> findPendingRecords();

    /**
     * 统计指定状态的记录数量
     */
    @Select("SELECT COUNT(*) FROM detection_records WHERE process_status = #{processStatus}")
    long countByProcessStatus(String processStatus);

    /**
     * 删除过期的检测记录（清理历史数据）
     */
    @Delete("DELETE FROM detection_records WHERE created_time < #{beforeTime}")
    int deleteOldRecords(@Param("beforeTime") LocalDateTime beforeTime);
}
