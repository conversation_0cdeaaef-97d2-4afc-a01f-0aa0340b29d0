package com.ybda.mapper;

import com.ybda.entity.DetectionDetail;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 检测详情Mapper接口
 */
@Mapper
public interface DetectionDetailMapper {

    /**
     * 插入检测详情
     */
    @Insert("INSERT INTO detection_details (detection_record_id, type, name, model_source, class_id, " +
            "bbox_x1, bbox_y1, bbox_x2, bbox_y2, track_id, traffic_asset_id, created_time) " +
            "VALUES (#{detectionRecordId}, #{type}, #{name}, #{modelSource}, #{classId}, " +
            "#{bboxX1}, #{bboxY1}, #{bboxX2}, #{bboxY2}, #{trackId}, #{trafficAssetId}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertDetail(DetectionDetail detail);

    /**
     * 批量插入检测详情
     */
    @Insert("<script>" +
            "INSERT INTO detection_details (detection_record_id, type, name, model_source, class_id, " +
            "bbox_x1, bbox_y1, bbox_x2, bbox_y2, track_id, traffic_asset_id, created_time) VALUES " +
            "<foreach collection='details' item='detail' separator=','>" +
            "(#{detail.detectionRecordId}, #{detail.type}, #{detail.name}, #{detail.modelSource}, #{detail.classId}, " +
            "#{detail.bboxX1}, #{detail.bboxY1}, #{detail.bboxX2}, #{detail.bboxY2}, #{detail.trackId}, " +
            "#{detail.trafficAssetId}, #{detail.createdTime})" +
            "</foreach>" +
            "</script>")
    int batchInsertDetails(@Param("details") List<DetectionDetail> details);

    /**
     * 更新关联的资产ID
     */
    @Update("UPDATE detection_details SET traffic_asset_id = #{trafficAssetId} WHERE id = #{id}")
    int updateTrafficAssetId(@Param("id") Long id, @Param("trafficAssetId") String trafficAssetId);

    /**
     * 根据检测记录ID查找详情
     */
    @Select("SELECT * FROM detection_details WHERE detection_record_id = #{detectionRecordId}")
    List<DetectionDetail> findByDetectionRecordId(Long detectionRecordId);

    /**
     * 根据类型查找检测详情
     */
    @Select("SELECT * FROM detection_details WHERE type = #{type}")
    List<DetectionDetail> findByType(String type);

    /**
     * 根据跟踪ID查找检测详情
     */
    @Select("SELECT * FROM detection_details WHERE track_id = #{trackId}")
    List<DetectionDetail> findByTrackId(Integer trackId);

    /**
     * 根据交通资产ID查找相关检测详情
     */
    @Select("SELECT * FROM detection_details WHERE traffic_asset_id = #{trafficAssetId}")
    List<DetectionDetail> findByTrafficAssetId(String trafficAssetId);

    /**
     * 查找未关联到交通资产的检测详情
     */
    @Select("SELECT * FROM detection_details WHERE traffic_asset_id IS NULL")
    List<DetectionDetail> findUnlinkedDetails();

    /**
     * 统计各类型的检测数量
     */
    @Select("SELECT type, COUNT(*) as count FROM detection_details GROUP BY type")
    List<Object[]> countByType();

    /**
     * 删除指定检测记录的所有详情
     */
    @Delete("DELETE FROM detection_details WHERE detection_record_id = #{detectionRecordId}")
    int deleteByDetectionRecordId(Long detectionRecordId);
}
