-- 资产巡检系统数据库表结构
-- 使用前请先创建数据库：CREATE DATABASE asset_inspection CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 1. 交通资产表
CREATE TABLE IF NOT EXISTS traffic_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_id VARCHAR(64) NOT NULL UNIQUE COMMENT '资产唯一标识符',
    type VARCHAR(32) NOT NULL COMMENT '资产类型：ground_marking/overhead_sign/traffic_light/barrier',
    name VARCHAR(128) NOT NULL COMMENT '资产名称',
    latitude DECIMAL(10,8) NOT NULL COMMENT '资产所在纬度',
    longitude DECIMAL(11,8) NOT NULL COMMENT '资产所在经度',
    first_detected_time DATETIME NOT NULL COMMENT '首次检测到该资产的时间',
    last_detected_time DATETIME NOT NULL COMMENT '最后一次检测到该资产的时间',
    detection_count INT NOT NULL DEFAULT 1 COMMENT '累计检测次数',
    status VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '资产状态：ACTIVE-活跃/MISSING-缺失/INACTIVE-不活跃',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_asset_id (asset_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_location (latitude, longitude),
    INDEX idx_last_detected (last_detected_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交通资产表';

-- 2. 检测记录表
CREATE TABLE IF NOT EXISTS detection_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    frame_id VARCHAR(64) NOT NULL COMMENT '帧ID，用于标识检测帧',
    timestamp BIGINT NOT NULL COMMENT 'Unix时间戳，检测时间',
    gps_latitude DECIMAL(10,8) COMMENT 'GPS纬度',
    gps_longitude DECIMAL(11,8) COMMENT 'GPS经度',
    device_id VARCHAR(64) COMMENT '设备标识符',
    process_status VARCHAR(16) NOT NULL DEFAULT 'PENDING' COMMENT '处理状态：PENDING-待处理/PROCESSED-已处理/FAILED-处理失败',
    raw_data TEXT COMMENT '原始JSON数据',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    processed_time DATETIME COMMENT '处理时间',
    INDEX idx_frame_id (frame_id),
    INDEX idx_process_status (process_status),
    INDEX idx_device_id (device_id),
    INDEX idx_created_time (created_time),
    INDEX idx_gps_location (gps_latitude, gps_longitude)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测记录表';

-- 3. 检测详情表
CREATE TABLE IF NOT EXISTS detection_details (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    detection_record_id BIGINT NOT NULL COMMENT '关联的检测记录ID',
    type VARCHAR(32) NOT NULL COMMENT '标志类型：ground_marking/overhead_sign/traffic_light/barrier',
    name VARCHAR(128) NOT NULL COMMENT '标志名称',
    model_source VARCHAR(256) COMMENT '模型推理路径',
    class_id INT NOT NULL COMMENT '标志分类ID',
    bbox_x1 DECIMAL(10,2) NOT NULL COMMENT '边界框坐标x1',
    bbox_y1 DECIMAL(10,2) NOT NULL COMMENT '边界框坐标y1',
    bbox_x2 DECIMAL(10,2) NOT NULL COMMENT '边界框坐标x2',
    bbox_y2 DECIMAL(10,2) NOT NULL COMMENT '边界框坐标y2',
    track_id INT NOT NULL COMMENT '跟踪ID，同一物体的唯一标识',
    traffic_asset_id VARCHAR(64) COMMENT '关联的交通资产ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_detection_record_id (detection_record_id),
    INDEX idx_type (type),
    INDEX idx_track_id (track_id),
    INDEX idx_traffic_asset_id (traffic_asset_id),
    FOREIGN KEY (detection_record_id) REFERENCES detection_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检测详情表';

-- 4. GPS轨迹表（核心：解决POST接口只有识别到才调用的问题）
CREATE TABLE IF NOT EXISTS gps_tracks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    latitude DECIMAL(10,8) NOT NULL COMMENT '纬度',
    longitude DECIMAL(11,8) NOT NULL COMMENT '经度',
    gps_timestamp BIGINT COMMENT 'GPS时间戳',
    accuracy DECIMAL(8,2) COMMENT 'GPS精度',
    altitude DECIMAL(8,2) COMMENT '海拔高度',
    speed DECIMAL(8,2) COMMENT '速度(km/h)',
    bearing DECIMAL(8,2) COMMENT '方向角',
    device_id VARCHAR(64) COMMENT '设备ID',
    processed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已处理（用于资产匹配检测）',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    INDEX idx_location (latitude, longitude),
    INDEX idx_processed (processed),
    INDEX idx_created_time (created_time),
    INDEX idx_device_id (device_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GPS轨迹表';

-- 5. 报警记录表（可选，用于记录报警历史）
CREATE TABLE IF NOT EXISTS alert_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    alert_type VARCHAR(32) NOT NULL COMMENT '报警类型：MISSING_ASSET-资产缺失/SYSTEM_ERROR-系统错误',
    asset_id VARCHAR(64) COMMENT '相关资产ID',
    alert_message TEXT NOT NULL COMMENT '报警消息',
    alert_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '报警时间',
    status VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '报警状态：ACTIVE-活跃/RESOLVED-已解决',
    resolved_time DATETIME COMMENT '解决时间',
    INDEX idx_alert_type (alert_type),
    INDEX idx_asset_id (asset_id),
    INDEX idx_alert_time (alert_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报警记录表';

-- 插入一些示例数据（可选）
-- INSERT INTO traffic_assets (asset_id, type, name, latitude, longitude, first_detected_time, last_detected_time, detection_count, status) 
-- VALUES 
-- ('ASSET_DEMO001', 'overhead_sign', '限速标志', 39.9042, 116.4074, NOW(), NOW(), 1, 'ACTIVE'),
-- ('ASSET_DEMO002', 'traffic_light', '交通信号灯', 39.9043, 116.4075, NOW(), NOW(), 1, 'ACTIVE'),
-- ('ASSET_DEMO003', 'ground_marking', '车道线', 39.9044, 116.4076, NOW(), NOW(), 1, 'ACTIVE');
