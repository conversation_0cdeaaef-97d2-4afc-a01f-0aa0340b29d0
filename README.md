# 资产巡检车辆系统 (Asset Inspection Vehicle)

## 📋 项目概述

本项目是一个基于Spring Boot的交通资产巡检系统，主要功能包括：
- 接收视频分析系统的交通标志检测数据
- 从Redis获取GPS位置信息
- 进行数据融合处理，生成交通资产信息
- 提供资产管理和查询功能

## 🏗️ 系统架构

### 核心模块
1. **交通资产识别接收服务** - 接收POST请求，处理视频分析结果
2. **数据融合处理服务** - 将GPS数据与识别数据关联
3. **资产管理服务** - 存储和管理交通资产信息

### 技术栈
- **后端框架**: Spring Boot 3.5.4
- **数据库**: MySQL 8.0 + JPA/Hibernate
- **缓存**: Redis (GPS数据存储)
- **构建工具**: Maven
- **Java版本**: JDK 17

## 📊 数据库设计

### 主要数据表

#### 1. traffic_assets (交通资产表)
- `id` - 主键
- `asset_id` - 资产唯一标识
- `type` - 资产类型 (ground_marking/overhead_sign/traffic_light/barrier)
- `name` - 资产名称
- `latitude/longitude` - 资产位置坐标
- `first_detected_time` - 首次检测时间
- `last_detected_time` - 最后检测时间
- `detection_count` - 检测次数
- `status` - 资产状态

#### 2. detection_records (检测记录表)
- `id` - 主键
- `frame_id` - 帧ID
- `timestamp` - 检测时间戳
- `gps_latitude/gps_longitude` - GPS坐标
- `device_id` - 设备标识
- `process_status` - 处理状态
- `raw_data` - 原始JSON数据

#### 3. detection_details (检测详情表)
- `id` - 主键
- `detection_record_id` - 关联检测记录
- `type` - 标志类型
- `name` - 标志名称
- `model_source` - 模型推理路径
- `class_id` - 分类ID
- `bbox_x1/y1/x2/y2` - 边界框坐标
- `track_id` - 跟踪ID
- `traffic_asset_id` - 关联资产ID

## 🚀 API接口

### 检测数据接收接口
```
POST /api/detection/receive
Content-Type: application/json

{
  "frame_id": "frame_001",
  "timestamp": **********,
  "signs": [
    {
      "type": "overhead_sign",
      "name": "限速标志",
      "model_source": "#1→#7→#4",
      "class_id": 1,
      "bbox": [100, 200, 300, 400],
      "track_id": 123
    }
  ],
  "device_id": "camera_001"
}
```

### 资产管理接口
- `GET /api/assets` - 获取资产列表（支持分页和筛选）
- `GET /api/assets/{id}` - 获取资产详情
- `GET /api/assets/bounds` - 地理范围查询
- `GET /api/assets/statistics` - 资产统计信息

### 系统状态接口
- `GET /api/detection/status` - 获取处理状态统计
- `GET /api/detection/health` - 健康检查

## ⚙️ 配置说明

### 数据库配置
```properties
spring.datasource.url=********************************************
spring.datasource.username=root
spring.datasource.password=123456
```

### Redis配置
```properties
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=0
```

### GPS数据格式
Redis中key为"gps"的数据应包含以下字段：
```json
{
  "latitude": 39.9042,
  "longitude": 116.4074,
  "timestamp": **********,
  "accuracy": 5.0,
  "altitude": 50.0,
  "speed": 30.0,
  "bearing": 90.0
}
```

## 🔧 部署说明

### 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 启动步骤
1. 创建数据库 `asset_inspection`
2. 配置Redis服务并确保GPS数据正常写入
3. 修改 `application.properties` 中的数据库和Redis连接信息
4. 运行 `mvn spring-boot:run` 启动应用
5. 应用将在 http://localhost:8080 启动

### 数据库初始化
应用启动时会自动创建数据表（hibernate.ddl-auto=update）

## 📈 核心业务流程

### 数据处理流程
1. **接收检测数据** - 视频分析系统POST数据到 `/api/detection/receive`
2. **获取GPS信息** - 从Redis获取当前GPS坐标
3. **数据融合处理** - 将检测数据与GPS坐标关联
4. **资产识别** - 判断是否为新资产或更新现有资产
5. **数据存储** - 保存到数据库中

### 资产融合逻辑
- 在10米范围内查找相同类型和名称的资产
- 如果找到匹配资产，更新检测次数和最后检测时间
- 如果未找到，创建新的交通资产记录

## 🔍 监控和日志

### 日志配置
- 应用日志级别：DEBUG
- Redis操作日志：DEBUG
- 控制台输出格式：时间戳 + 线程 + 级别 + 类名 + 消息

### 监控指标
- 待处理检测记录数量
- 处理失败记录数量
- 资产总数统计
- 活跃/非活跃资产统计

## 📝 开发注意事项

1. **GPS数据依赖** - 确保Redis中GPS数据格式正确且实时更新
2. **坐标精度** - 数据库中坐标字段使用高精度存储
3. **异常处理** - 完整的全局异常处理机制
4. **数据验证** - 使用Bean Validation进行请求参数验证
5. **事务管理** - 数据融合过程使用事务保证数据一致性

## 🚧 后续扩展

- 添加资产查询和搜索功能
- 实现资产状态管理（激活/停用）
- 添加地图可视化界面
- 实现资产变更历史记录
- 添加数据导出功能
- 集成消息队列处理大量检测数据
