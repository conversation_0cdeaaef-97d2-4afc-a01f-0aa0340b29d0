# 资产巡检车辆系统 (Asset Inspection Vehicle)

## 📋 项目概述

本项目是一个基于Spring Boot的智能交通资产巡检监控系统，核心功能包括：
- **接收视频分析数据** - 处理来自视频分析系统的交通标志检测结果
- **GPS位置关联** - 从Redis获取实时GPS位置信息
- **资产变化检测** - 智能检测同一位置的资产增减变化
- **自动报警机制** - 资产缺失时自动触发报警
- **资产管理** - 完整的交通资产生命周期管理

## 🎯 核心业务逻辑

### 关键问题解决方案
**问题**: POST接口只有识别到标牌才会调用，GPS每秒更新，导致无法检测资产缺失
**解决**: GPS轨迹 + 资产匹配架构

### 双重检测机制
1. **实时检测数据处理** - 处理POST请求，记录检测到的资产
2. **GPS轨迹资产匹配** - 持续记录GPS轨迹，检测经过但未识别的资产

### 智能缺失检测算法（暂存+延迟检测）
1. **GPS轨迹记录**: 每秒从Redis获取GPS数据并存储
2. **实时位置检查**: 每秒检查GPS是否经过已知资产位置（15米范围内）
3. **创建待确认记录**: 发现资产位置时立即创建"待确认记录"，状态为PENDING
4. **延迟验证**: 2分钟后检查是否收到对应的POST请求
5. **双向确认**: POST请求到达时也会确认相关的待确认记录
6. **缺失判断**: 2分钟内没有POST请求 = 资产缺失
7. **自动报警**: 标记资产为MISSING状态并触发详细报警

## 🏗️ 系统架构

### 核心模块
1. **检测数据接收服务** - 接收视频分析POST请求，记录检测到的资产
2. **GPS轨迹记录服务** - 每秒记录GPS轨迹，解决POST接口局限性
3. **资产缺失检测服务** - 通过GPS轨迹匹配已知资产位置，检测缺失
4. **资产变化检测服务** - 处理新增资产的识别和入库
5. **报警服务** - 处理资产缺失等异常情况
6. **资产管理服务** - 资产CRUD和统计查询

### 技术栈
- **后端框架**: Spring Boot 3.5.4
- **数据库**: MySQL 8.0 + MyBatis
- **缓存**: Redis (GPS数据存储)
- **构建工具**: Maven
- **Java版本**: JDK 17

## 📊 数据库设计

### 主要数据表

#### 1. traffic_assets (交通资产表)
- `id` - 主键
- `asset_id` - 资产唯一标识
- `type` - 资产类型 (ground_marking/overhead_sign/traffic_light/barrier)
- `name` - 资产名称
- `latitude/longitude` - 资产位置坐标
- `first_detected_time` - 首次检测时间
- `last_detected_time` - 最后检测时间
- `detection_count` - 检测次数
- `status` - 资产状态 (ACTIVE/MISSING/INACTIVE)

#### 2. detection_records (检测记录表)
- `id` - 主键
- `frame_id` - 帧ID（每次检测都不同）
- `timestamp` - 检测时间戳
- `gps_latitude/gps_longitude` - GPS坐标
- `device_id` - 设备标识
- `process_status` - 处理状态
- `raw_data` - 原始JSON数据

#### 3. detection_details (检测详情表)
- `id` - 主键
- `detection_record_id` - 关联检测记录
- `type` - 标志类型
- `name` - 标志名称
- `model_source` - 模型推理路径
- `class_id` - 分类ID
- `bbox_x1/y1/x2/y2` - 边界框坐标
- `track_id` - 跟踪ID
- `traffic_asset_id` - 关联资产ID

#### 4. gps_tracks (GPS轨迹表) 🔑核心表
- `id` - 主键
- `latitude/longitude` - GPS坐标
- `gps_timestamp` - GPS时间戳
- `accuracy/altitude/speed/bearing` - GPS详细信息
- `device_id` - 设备标识
- `processed` - 是否已处理（用于资产匹配检测）
- `created_time` - 记录时间

#### 5. pending_asset_checks (待确认资产检查表) 🆕核心表
- `id` - 主键
- `gps_track_id` - 关联的GPS轨迹ID
- `expected_asset_id` - 预期的资产ID
- `asset_type/asset_name` - 资产类型和名称
- `gps_latitude/gps_longitude` - GPS位置坐标
- `passed_time` - 经过时间
- `status` - 检查状态 (PENDING/CONFIRMED/MISSING)
- `confirmed_detection_id` - 确认的检测记录ID
- `confirmed_time` - 确认时间

#### 6. alert_records (报警记录表)
- `id` - 主键
- `alert_type` - 报警类型
- `asset_id` - 相关资产ID
- `alert_message` - 报警消息
- `alert_time` - 报警时间
- `status` - 报警状态

## 🚀 API接口

### 检测数据接收接口
```
POST /api/detection/receive
Content-Type: application/json

{
  "frame_id": "frame_001",
  "timestamp": **********,
  "signs": [
    {
      "type": "overhead_sign",
      "name": "限速标志",
      "model_source": "#1→#7→#4",
      "class_id": 1,
      "bbox": [100, 200, 300, 400],
      "track_id": 123
    }
  ],
  "device_id": "camera_001"
}
```

### 资产管理接口
- `GET /api/assets` - 获取资产列表（支持分页和筛选）
- `GET /api/assets/{id}` - 获取资产详情
- `GET /api/assets/bounds` - 地理范围查询
- `GET /api/assets/statistics` - 资产统计信息

### GPS轨迹管理接口 🆕
- `GET /api/gps/status` - 获取GPS轨迹统计信息
- `GET /api/gps/tracks` - 获取指定时间范围内的GPS轨迹
- `POST /api/gps/process` - 手动触发GPS轨迹处理
- `POST /api/gps/detect-missing` - 手动触发指定区域的资产缺失检测
- `GET /api/gps/health` - GPS服务健康检查

### 系统状态接口
- `GET /api/detection/status` - 获取处理状态统计
- `GET /api/detection/health` - 健康检查

## ⚙️ 配置说明

### 数据库配置
```properties
spring.datasource.url=********************************************
spring.datasource.username=root
spring.datasource.password=123456
```

### MyBatis配置
```properties
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.ybda.entity
mybatis.configuration.map-underscore-to-camel-case=true
```

### Redis配置
```properties
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=0
```

### GPS数据格式
Redis中key为"gps"的数据应包含以下字段：
```json
{
  "latitude": 39.9042,
  "longitude": 116.4074,
  "timestamp": **********,
  "accuracy": 5.0,
  "altitude": 50.0,
  "speed": 30.0,
  "bearing": 90.0
}
```

## 🔧 部署说明

### 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 启动步骤
1. 创建数据库 `asset_inspection`
2. 执行 `src/main/resources/sql/schema.sql` 创建数据表
3. 配置Redis服务并确保GPS数据正常写入
4. 修改 `application.properties` 中的数据库和Redis连接信息
5. 运行 `mvn spring-boot:run` 启动应用
6. 应用将在 http://localhost:8080 启动

### 数据库初始化
使用提供的SQL脚本创建数据表，支持完整的索引和外键约束

## 📈 核心业务流程

### 双重检测架构流程

#### 流程A：检测数据处理（POST触发）
1. **接收检测数据** - 视频分析系统POST数据到 `/api/detection/receive`
2. **记录检测信息** - 保存检测记录和详情到数据库
3. **处理新增资产** - 识别并入库新发现的资产

#### 流程B：GPS轨迹资产匹配（定时任务）
1. **GPS轨迹记录** - 每秒从Redis获取GPS数据并存储
2. **实时位置检查** - 每秒检查GPS是否经过已知资产位置（15米范围）
3. **创建待确认记录** - 发现资产位置时立即创建PENDING状态的记录
4. **延迟验证** - 每10秒检查超时的待确认记录（2分钟超时）
5. **双向确认** - POST请求到达时确认相关待确认记录为CONFIRMED
6. **缺失判断** - 超时且未确认 = 资产缺失
7. **自动报警** - 标记MISSING状态并触发报警

### 智能报警机制
- **资产缺失报警**: 详细的控制台报警信息
- **GPS异常报警**: Redis数据无效时触发
- **系统异常报警**: 处理失败时记录错误信息
- **报警信息包含**: 资产详情、位置、缺失时长、GPS轨迹信息等

## 🔍 监控和日志

### 日志配置
- 应用日志级别：DEBUG
- Redis操作日志：DEBUG
- 控制台输出格式：时间戳 + 线程 + 级别 + 类名 + 消息

### 监控指标
- 待处理检测记录数量
- 处理失败记录数量
- 资产总数统计
- 活跃/非活跃资产统计

## 📝 开发注意事项

1. **核心问题解决** - GPS轨迹记录解决了"POST接口只有识别到才调用"的关键问题
2. **双重检测机制** - 检测数据处理 + GPS轨迹匹配，确保不遗漏任何资产变化
3. **定时任务依赖** - 系统依赖定时任务进行GPS轨迹记录和处理，确保服务正常运行
4. **GPS数据质量** - 确保Redis中GPS数据格式正确、精度足够且实时更新
5. **距离阈值调优** - 资产匹配距离（15米）和时间窗口（2分钟）可根据实际情况调整
6. **数据库性能** - GPS轨迹表数据量大，注意定期清理和索引优化
7. **报警机制** - 避免重复报警，已MISSING状态的资产不会重复触发报警
8. **事务管理** - 关键操作使用事务保证数据一致性

## 🚨 报警示例

当检测到资产缺失时，系统会输出如下报警信息：
```
🚨 资产缺失报警 🚨
报警时间: 2024-01-15 14:30:25
资产ID: ASSET_ABC123
资产类型: 空中标牌
资产名称: 限速标志
资产位置: 纬度 39.9042, 经度 116.4074
首次检测时间: 2024-01-10 09:15:30
最后检测时间: 2024-01-14 16:20:15
历史检测次数: 15
缺失时长: 约 1 天
==========================================
```

## 🚧 后续扩展

- 实现邮件/短信报警通知
- 添加Web管理界面和地图可视化
- 实现资产变更历史记录和趋势分析
- 添加批量资产导入导出功能
- 集成消息队列处理大量检测数据
- 实现资产巡检路线规划和优化
